2024-01-01 10:00:01 INFO Application started successfully
2024-01-01 10:00:02 INFO Database connection established
2024-01-01 10:00:03 ERROR Failed to connect to Redis server: Connection timeout
    at RedisClient.connect(RedisClient.java:45)
    at ConnectionPool.getConnection(ConnectionPool.java:23)
    at DataService.retrieveData(DataService.java:67)
    Caused by: java.net.ConnectException: Connection refused
2024-01-01 10:00:04 WARN Memory usage is at 85%
2024-01-01 10:00:05 INFO Processing user request for user_id=12345
2024-01-01 10:00:06 ERROR SQL query failed: Table 'users' doesn't exist
    Query: SELECT * FROM users WHERE id = ?
    Parameters: [12345]
    at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2440)
    at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2183)
2024-01-01 10:00:07 INFO Request processed successfully
2024-01-01 10:00:08 ERROR HTTP 500 Internal Server Error
    java.lang.NullPointerException: Cannot invoke method on null object
    at com.example.UserController.getUser(UserController.java:42)
    at com.example.UserController.handleRequest(UserController.java:28)
    at java.base/java.lang.reflect.Method.invoke(Method.java:566)
2024-01-01 10:00:09 INFO Scheduled backup started
2024-01-01 10:00:10 ERROR Backup failed: Insufficient disk space
    Available space: 145 MB
    Required space: 2.5 GB
    Location: /var/backups/
2024-01-01 10:00:11 WARN CPU usage spike detected: 95%
2024-01-01 10:00:12 INFO User authentication successful
2024-01-01 10:00:13 ERROR Authentication failed: Invalid credentials
    Username: admin
    IP Address: *************
    Attempted at: 2024-01-01 10:00:13
    Reason: Password mismatch
2024-01-01 10:00:14 INFO API endpoint /health responded with 200
2024-01-01 10:00:15 ERROR Network timeout occurred
    Timeout after 30000ms
    Target: external-api.example.com:443
    Last successful connection: 2024-01-01 09:45:22
2024-01-01 10:00:16 CRITICAL OutOfMemoryError: Java heap space
    at java.base/java.lang.StringConcatenationHelper.newArray(StringConcatenationHelper.java:520)
    at java.base/java.lang.StringConcatenationHelper.simpleConcat(StringConcatenationHelper.java:496)
    at com.example.DataProcessor.processLargeDataSet(DataProcessor.java:156)
    at com.example.DataProcessor.run(DataProcessor.java:89)
    Heap dump created: /tmp/heapdump-2024-01-01-10-00-16.hprof
2024-01-01 10:00:17 ERROR Cascade failure detected
2024-01-01 10:00:17 ERROR Service A is down
2024-01-01 10:00:17 ERROR Service B is down
2024-01-01 10:00:17 ERROR Service C is down
2024-01-01 10:00:17 ERROR Service D is down
2024-01-01 10:00:17 ERROR Service E is down
2024-01-01 10:00:18 WARN System recovering from cascade failure
2024-01-01 10:00:19 INFO Services restarting
2024-01-01 10:00:20 INFO Application stabilized
