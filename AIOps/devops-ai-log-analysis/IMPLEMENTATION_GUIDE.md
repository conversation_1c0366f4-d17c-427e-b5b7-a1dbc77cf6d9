# DevOps AI Log Analysis - Quick Implementation Guide

## 🚀 Top Priority Enhancements (Implementation Ready)

This guide provides ready-to-implement code for the most impactful framework enhancements.

### 1. **Natural Language Query Interface**

#### A. Create LLM Integration Module
```python
# src/ai_enhanced/llm_integration.py
import openai
import json
import re
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

class NaturalLanguageQueryProcessor:
    """
    Process natural language queries and convert them to structured log queries.
    """
    
    def __init__(self, api_key: str):
        self.client = openai.OpenAI(api_key=api_key)
        self.query_patterns = {
            'time_range': r'(?:in the )?(?:last|past) (\d+) (minute|hour|day|week)s?',
            'service': r'(?:from|in) (?:service|app|application) (\w+)',
            'error_type': r'(error|exception|failure|crash|timeout|connection)',
            'severity': r'(critical|error|warning|info|debug)',
            'user_action': r'(login|logout|authentication|authorization|access)',
        }
    
    def process_query(self, query: str) -> Dict:
        """
        Convert natural language query to structured search parameters.
        """
        # Extract basic patterns
        patterns = self._extract_patterns(query)
        
        # Use LLM for complex interpretation
        llm_analysis = self._analyze_with_llm(query)
        
        # Combine results
        search_params = {
            'query': query,
            'time_range': patterns.get('time_range'),
            'service': patterns.get('service'),
            'error_types': patterns.get('error_types', []),
            'severity': patterns.get('severity'),
            'filters': llm_analysis.get('filters', {}),
            'sql_query': llm_analysis.get('sql_query', ''),
            'elasticsearch_query': llm_analysis.get('elasticsearch_query', {})
        }
        
        return search_params
    
    def _extract_patterns(self, query: str) -> Dict:
        """Extract basic patterns using regex."""
        patterns = {}
        
        # Time range extraction
        time_match = re.search(self.query_patterns['time_range'], query, re.IGNORECASE)
        if time_match:
            amount, unit = time_match.groups()
            patterns['time_range'] = self._calculate_time_range(int(amount), unit)
        
        # Service extraction
        service_match = re.search(self.query_patterns['service'], query, re.IGNORECASE)
        if service_match:
            patterns['service'] = service_match.group(1)
        
        # Error type extraction
        error_matches = re.findall(self.query_patterns['error_type'], query, re.IGNORECASE)
        if error_matches:
            patterns['error_types'] = error_matches
        
        # Severity extraction
        severity_match = re.search(self.query_patterns['severity'], query, re.IGNORECASE)
        if severity_match:
            patterns['severity'] = severity_match.group(1)
        
        return patterns
    
    def _analyze_with_llm(self, query: str) -> Dict:
        """Use LLM to analyze complex queries."""
        system_prompt = """
        You are a log analysis expert. Convert natural language queries into structured search parameters.
        
        Return a JSON object with:
        - filters: dict of key-value pairs for filtering
        - sql_query: SQL query for structured data
        - elasticsearch_query: Elasticsearch query DSL
        - explanation: brief explanation of the query
        
        Example input: "Show me all authentication failures from user-service in the last hour"
        Example output: {
            "filters": {
                "service": "user-service",
                "event_type": "authentication",
                "status": "failure",
                "time_range": "1h"
            },
            "sql_query": "SELECT * FROM logs WHERE service = 'user-service' AND message LIKE '%authentication%' AND message LIKE '%fail%' AND timestamp > NOW() - INTERVAL '1 hour'",
            "elasticsearch_query": {
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"service": "user-service"}},
                            {"match": {"message": "authentication failure"}},
                            {"range": {"timestamp": {"gte": "now-1h"}}}
                        ]
                    }
                }
            },
            "explanation": "Finding authentication failures in user-service from the last hour"
        }
        """
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Query: {query}"}
                ],
                temperature=0.1
            )
            
            content = response.choices[0].message.content
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            
        except Exception as e:
            print(f"LLM analysis failed: {e}")
        
        return {}
    
    def _calculate_time_range(self, amount: int, unit: str) -> Tuple[datetime, datetime]:
        """Calculate datetime range from amount and unit."""
        now = datetime.now()
        
        if unit.lower().startswith('minute'):
            start_time = now - timedelta(minutes=amount)
        elif unit.lower().startswith('hour'):
            start_time = now - timedelta(hours=amount)
        elif unit.lower().startswith('day'):
            start_time = now - timedelta(days=amount)
        elif unit.lower().startswith('week'):
            start_time = now - timedelta(weeks=amount)
        else:
            start_time = now - timedelta(hours=1)  # Default to 1 hour
        
        return start_time, now

# Usage example
if __name__ == "__main__":
    processor = NaturalLanguageQueryProcessor("your-openai-api-key")
    
    queries = [
        "Show me all errors from the authentication service in the last hour",
        "Find critical failures in the payment system from yesterday",
        "What happened with user logins in the last 24 hours?",
        "Show me all database connection timeouts this week"
    ]
    
    for query in queries:
        result = processor.process_query(query)
        print(f"Query: {query}")
        print(f"Result: {json.dumps(result, indent=2, default=str)}")
        print("-" * 80)
```

### 2. **Real-Time Dashboard with WebSocket**

#### A. WebSocket Server Implementation
```python
# ui/websocket_server.py
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask import Flask, request
import json
import threading
import time
import queue
from anomaly_detection.advanced_anomaly_detection import RealTimeAnomalyBuffer

class RealTimeDashboard:
    """
    Real-time dashboard with WebSocket support for live log analysis.
    """
    
    def __init__(self, app: Flask):
        self.socketio = SocketIO(app, cors_allowed_origins="*")
        self.anomaly_buffer = RealTimeAnomalyBuffer()
        self.active_connections = set()
        self.log_queue = queue.Queue()
        
        # Register WebSocket event handlers
        self.socketio.on_event('connect', self.handle_connect)
        self.socketio.on_event('disconnect', self.handle_disconnect)
        self.socketio.on_event('join_room', self.handle_join_room)
        self.socketio.on_event('leave_room', self.handle_leave_room)
        
        # Start background monitoring
        self.monitoring_thread = threading.Thread(target=self._monitor_anomalies)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
    
    def handle_connect(self):
        """Handle new WebSocket connections."""
        print(f"Client connected: {request.sid}")
        self.active_connections.add(request.sid)
        
        # Send initial dashboard data
        initial_data = {
            'type': 'dashboard_init',
            'data': {
                'total_logs': len(self.anomaly_buffer.buffer),
                'anomalies_detected': len(self.anomaly_buffer.anomalies),
                'services_monitored': self._get_monitored_services(),
                'status': 'connected'
            }
        }
        emit('dashboard_update', initial_data)
    
    def handle_disconnect(self):
        """Handle WebSocket disconnections."""
        print(f"Client disconnected: {request.sid}")
        self.active_connections.discard(request.sid)
    
    def handle_join_room(self, data):
        """Handle room joining for targeted updates."""
        room = data.get('room', 'general')
        join_room(room)
        emit('status', {'message': f'Joined room: {room}'})
    
    def handle_leave_room(self, data):
        """Handle room leaving."""
        room = data.get('room', 'general')
        leave_room(room)
        emit('status', {'message': f'Left room: {room}'})
    
    def _monitor_anomalies(self):
        """Background thread to monitor for anomalies and broadcast updates."""
        while True:
            try:
                # Check for new anomalies
                if self.anomaly_buffer.anomalies:
                    latest_anomaly = self.anomaly_buffer.anomalies[-1]
                    
                    # Broadcast to all connected clients
                    update_data = {
                        'type': 'anomaly_detected',
                        'data': {
                            'anomaly': latest_anomaly,
                            'timestamp': time.time(),
                            'total_anomalies': len(self.anomaly_buffer.anomalies)
                        }
                    }
                    
                    self.socketio.emit('dashboard_update', update_data)
                
                # Send periodic status updates
                status_data = {
                    'type': 'status_update',
                    'data': {
                        'active_connections': len(self.active_connections),
                        'buffer_size': len(self.anomaly_buffer.buffer),
                        'processing_rate': self.anomaly_buffer.get_processing_rate(),
                        'timestamp': time.time()
                    }
                }
                
                self.socketio.emit('dashboard_update', status_data)
                
                time.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                print(f"Error in monitoring thread: {e}")
                time.sleep(10)
    
    def _get_monitored_services(self):
        """Get list of currently monitored services."""
        # Extract services from recent logs
        services = set()
        for log_entry in list(self.anomaly_buffer.buffer)[-100:]:  # Last 100 logs
            if 'service' in log_entry:
                services.add(log_entry['service'])
        return list(services)
    
    def add_log_entry(self, log_entry):
        """Add new log entry for real-time processing."""
        self.anomaly_buffer.add_log_entry(log_entry)
        
        # Broadcast log entry to subscribers
        update_data = {
            'type': 'new_log',
            'data': {
                'log_entry': log_entry,
                'timestamp': time.time()
            }
        }
        
        self.socketio.emit('log_update', update_data, room='logs')

# Integration with Flask app
def create_real_time_app():
    """Create Flask app with real-time capabilities."""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'your-secret-key-here'
    
    # Initialize real-time dashboard
    dashboard = RealTimeDashboard(app)
    
    @app.route('/realtime')
    def realtime_dashboard():
        return render_template('realtime_dashboard.html')
    
    return app, dashboard

if __name__ == "__main__":
    app, dashboard = create_real_time_app()
    dashboard.socketio.run(app, host='0.0.0.0', port=5000, debug=True)
```

### 3. **Kubernetes Integration Module**

```python
# src/integrations/kubernetes_integration.py
from kubernetes import client, config, watch
import json
import threading
import time
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta

class KubernetesLogCollector:
    """
    Kubernetes integration for real-time log collection and pod monitoring.
    """
    
    def __init__(self, namespace: str = "default", config_file: Optional[str] = None):
        """
        Initialize Kubernetes client.
        
        Args:
            namespace: Kubernetes namespace to monitor
            config_file: Path to kubeconfig file (None for in-cluster config)
        """
        self.namespace = namespace
        self.is_running = False
        self.log_callback = None
        self.pod_callback = None
        
        # Load Kubernetes config
        try:
            if config_file:
                config.load_kube_config(config_file)
            else:
                # Try in-cluster config first, then local config
                try:
                    config.load_incluster_config()
                except:
                    config.load_kube_config()
        except Exception as e:
            raise Exception(f"Failed to load Kubernetes config: {e}")
        
        # Initialize API clients
        self.v1 = client.CoreV1Api()
        self.apps_v1 = client.AppsV1Api()
        
        # Monitoring threads
        self.log_thread = None
        self.pod_thread = None
    
    def start_monitoring(self, log_callback: Callable = None, pod_callback: Callable = None):
        """
        Start monitoring Kubernetes logs and pod events.
        
        Args:
            log_callback: Function to call when new logs are received
            pod_callback: Function to call when pod events occur
        """
        self.log_callback = log_callback
        self.pod_callback = pod_callback
        self.is_running = True
        
        # Start log monitoring thread
        if log_callback:
            self.log_thread = threading.Thread(target=self._monitor_logs)
            self.log_thread.daemon = True
            self.log_thread.start()
        
        # Start pod monitoring thread
        if pod_callback:
            self.pod_thread = threading.Thread(target=self._monitor_pods)
            self.pod_thread.daemon = True
            self.pod_thread.start()
        
        print(f"Started monitoring namespace: {self.namespace}")
    
    def stop_monitoring(self):
        """Stop monitoring."""
        self.is_running = False
        print("Stopped Kubernetes monitoring")
    
    def _monitor_logs(self):
        """Monitor logs from all pods in the namespace."""
        print(f"Starting log monitoring for namespace: {self.namespace}")
        
        while self.is_running:
            try:
                # Get all pods in the namespace
                pods = self.v1.list_namespaced_pod(namespace=self.namespace)
                
                for pod in pods.items:
                    if pod.status.phase == "Running":
                        self._stream_pod_logs(pod)
                
                time.sleep(30)  # Check for new pods every 30 seconds
                
            except Exception as e:
                print(f"Error monitoring logs: {e}")
                time.sleep(60)  # Wait before retrying
    
    def _stream_pod_logs(self, pod):
        """Stream logs from a specific pod."""
        try:
            # Get recent logs
            logs = self.v1.read_namespaced_pod_log(
                name=pod.metadata.name,
                namespace=self.namespace,
                tail_lines=10,
                timestamps=True
            )
            
            # Process each log line
            for line in logs.split('\n'):
                if line.strip():
                    log_entry = self._parse_log_line(line, pod)
                    if log_entry and self.log_callback:
                        self.log_callback(log_entry)
        
        except Exception as e:
            print(f"Error streaming logs from pod {pod.metadata.name}: {e}")
    
    def _monitor_pods(self):
        """Monitor pod events (creation, deletion, failures)."""
        print(f"Starting pod monitoring for namespace: {self.namespace}")
        
        w = watch.Watch()
        
        while self.is_running:
            try:
                # Watch pod events
                for event in w.stream(
                    self.v1.list_namespaced_pod,
                    namespace=self.namespace,
                    timeout_seconds=300
                ):
                    if not self.is_running:
                        break
                    
                    event_type = event['type']
                    pod = event['object']
                    
                    pod_event = {
                        'timestamp': datetime.now().isoformat(),
                        'type': 'kubernetes_pod_event',
                        'event_type': event_type,
                        'pod_name': pod.metadata.name,
                        'namespace': pod.metadata.namespace,
                        'phase': pod.status.phase,
                        'node': pod.spec.node_name,
                        'labels': pod.metadata.labels or {},
                        'annotations': pod.metadata.annotations or {}
                    }
                    
                    if self.pod_callback:
                        self.pod_callback(pod_event)
                
            except Exception as e:
                print(f"Error monitoring pods: {e}")
                time.sleep(30)
    
    def _parse_log_line(self, line: str, pod) -> Optional[Dict]:
        """Parse a log line into structured format."""
        try:
            # Extract timestamp if present
            timestamp = None
            message = line
            
            if line.startswith('2024-') or line.startswith('2023-'):
                # Parse timestamp
                parts = line.split(' ', 1)
                if len(parts) == 2:
                    timestamp = parts[0]
                    message = parts[1]
            
            # Create structured log entry
            log_entry = {
                'timestamp': timestamp or datetime.now().isoformat(),
                'source': 'kubernetes',
                'namespace': pod.metadata.namespace,
                'pod_name': pod.metadata.name,
                'container': pod.spec.containers[0].name if pod.spec.containers else 'unknown',
                'node': pod.spec.node_name,
                'labels': pod.metadata.labels or {},
                'message': message.strip(),
                'level': self._extract_log_level(message),
                'service': self._extract_service_name(pod)
            }
            
            return log_entry
            
        except Exception as e:
            print(f"Error parsing log line: {e}")
            return None
    
    def _extract_log_level(self, message: str) -> str:
        """Extract log level from message."""
        message_upper = message.upper()
        
        if 'ERROR' in message_upper or 'FATAL' in message_upper:
            return 'ERROR'
        elif 'WARN' in message_upper:
            return 'WARNING'
        elif 'INFO' in message_upper:
            return 'INFO'
        elif 'DEBUG' in message_upper:
            return 'DEBUG'
        else:
            return 'INFO'
    
    def _extract_service_name(self, pod) -> str:
        """Extract service name from pod metadata."""
        labels = pod.metadata.labels or {}
        
        # Try common service label patterns
        service_labels = ['app', 'service', 'component', 'app.kubernetes.io/name']
        
        for label in service_labels:
            if label in labels:
                return labels[label]
        
        # Fallback to pod name
        return pod.metadata.name
    
    def get_cluster_info(self) -> Dict:
        """Get cluster information and statistics."""
        try:
            # Get node information
            nodes = self.v1.list_node()
            
            # Get namespace information
            namespaces = self.v1.list_namespace()
            
            # Get pod information
            pods = self.v1.list_namespaced_pod(namespace=self.namespace)
            
            # Get deployment information
            deployments = self.apps_v1.list_namespaced_deployment(namespace=self.namespace)
            
            cluster_info = {
                'timestamp': datetime.now().isoformat(),
                'cluster_stats': {
                    'total_nodes': len(nodes.items),
                    'total_namespaces': len(namespaces.items),
                    'pods_in_namespace': len(pods.items),
                    'deployments_in_namespace': len(deployments.items)
                },
                'nodes': [
                    {
                        'name': node.metadata.name,
                        'status': 'Ready' if any(
                            condition.type == 'Ready' and condition.status == 'True'
                            for condition in node.status.conditions
                        ) else 'NotReady',
                        'version': node.status.node_info.kubelet_version,
                        'os': node.status.node_info.os_image
                    }
                    for node in nodes.items
                ],
                'pods': [
                    {
                        'name': pod.metadata.name,
                        'phase': pod.status.phase,
                        'ready': sum(1 for container in pod.status.container_statuses or [] if container.ready),
                        'restarts': sum(container.restart_count for container in pod.status.container_statuses or []),
                        'age': (datetime.now() - pod.metadata.creation_timestamp.replace(tzinfo=None)).days
                    }
                    for pod in pods.items
                ]
            }
            
            return cluster_info
            
        except Exception as e:
            print(f"Error getting cluster info: {e}")
            return {'error': str(e)}
```

### 4. **Quick Setup Script**

```bash
#!/bin/bash
# setup_enhanced_features.sh

echo "🚀 Setting up Enhanced DevOps AI Log Analysis Features"
echo "=================================================="

# Create necessary directories
mkdir -p src/ai_enhanced
mkdir -p src/integrations
mkdir -p ui/templates

# Install additional dependencies
echo "📦 Installing enhanced dependencies..."
pip install openai flask-socketio kubernetes redis celery

# Set up environment variables
echo "🔧 Setting up environment variables..."
cat > .env << 'EOF'
# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Flask Configuration
FLASK_SECRET_KEY=your-secret-key-here

# Redis Configuration (for caching)
REDIS_HOST=localhost
REDIS_PORT=6379

# Kubernetes Configuration
KUBERNETES_NAMESPACE=default
KUBERNETES_CONFIG_FILE=

# WebSocket Configuration
WEBSOCKET_PORT=5000
WEBSOCKET_HOST=0.0.0.0
EOF

echo "✅ Enhanced features setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Update your OpenAI API key in .env file"
echo "2. Install Kubernetes if needed: kubectl, minikube, or cloud cluster"
echo "3. Configure Redis if using caching features"
echo "4. Run the enhanced features"
echo ""
echo "🎯 New Features Available:"
echo "- Natural language log queries"
echo "- Real-time dashboard with WebSocket"
echo "- Kubernetes integration"
echo "- Advanced AI analytics"
echo "- Modern responsive UI"
```

## 🛠️ Implementation Priority

### Phase 1: Core Enhancements (Week 1-2)
1. **Natural Language Queries** - Implement LLM integration
2. **Real-time Dashboard** - Add WebSocket support
3. **Enhanced UI** - Modernize frontend with responsive design

### Phase 2: Production Features (Week 3-4)
1. **Kubernetes Integration** - Add container orchestration support
2. **Advanced Analytics** - Implement predictive modeling
3. **Security Features** - Add authentication and authorization

### Phase 3: Enterprise Features (Week 5-6)
1. **Scalability** - Add distributed processing
2. **Monitoring** - Add system health monitoring
3. **API Development** - Create comprehensive REST/GraphQL APIs

This implementation guide provides ready-to-use code that can be integrated into your existing system. Each component is designed to be modular and can be implemented independently based on your priorities.
