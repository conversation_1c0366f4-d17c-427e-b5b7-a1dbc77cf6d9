# DevOps AI Log Analysis

This project aims to provide a comprehensive solution for analyzing logs generated by various DevOps tools. It includes capabilities for log ingestion, preprocessing, error classification, root cause analysis, anomaly detection, and trend detection.

## Features

- **Log Ingestion**: Collect logs from various formats including text, XML, JSON, and time series data.
- **Preprocessing**: Clean and structure logs for further analysis using NLP techniques.
- **Error Classification**: Utilize machine learning models to classify errors found in logs.
- **Root Cause Analysis**: Identify potential causes of errors based on log patterns.
- **Anomaly Detection**: Spot unusual patterns in log data using statistical and ML-based models.
- **Trend Detection**: Analyze log data over time to identify trends and generate reports.

## Project Structure

```
devops-ai-log-analysis
├── src
│   ├── ingestion
│   ├── preprocessing
│   ├── error_classification
│   ├── root_cause_analysis
│   ├── anomaly_detection
│   ├── trend_detection
│   ├── utils
│   └── main.py
├── data
│   ├── raw
│   └── processed
├── models
├── requirements.txt
└── README.md
```

# DevOps AI Log Analysis

This project provides a comprehensive solution for analyzing logs generated by various DevOps tools. It includes capabilities for log ingestion, preprocessing, error classification, root cause analysis, anomaly detection, and trend detection, all accessible through a modern web interface.

## Features

### 🔍 **Core Analysis Features**
- **Log Ingestion**: Collect logs from various formats including text, XML, JSON, and time series data
- **Preprocessing**: Clean and structure logs for further analysis using NLP techniques
- **Error Classification**: Utilize machine learning models to classify errors found in logs
- **Root Cause Analysis**: Identify potential causes of errors based on log patterns with AI-powered insights
- **Anomaly Detection**: Spot unusual patterns in log data using statistical and ML-based models
- **Trend Detection**: Analyze log data over time to identify trends and generate reports

### 🌐 **Web Interface**
- **Modern Web UI**: Intuitive, responsive web interface for all analysis features
- **Interactive Dashboard**: Real-time visualization of analysis results
- **File Upload**: Drag-and-drop interface for multiple log formats
- **Visual Analytics**: Interactive charts and graphs for data exploration
- **Root Cause Recommendations**: Actionable insights with priority scoring
- **Demo Mode**: Try the system with sample data

### 🤖 **Advanced AI Features**
- **Machine Learning Models**: Ensemble methods, LSTM networks, and advanced pattern recognition
- **Generative AI**: Enhanced anomaly detection and root cause analysis
- **Real-time Processing**: Streaming analysis for large log files
- **Multi-format Support**: TXT, JSON, XML, CSV, LOG files with multiline support

## Project Structure

```
devops-ai-log-analysis/
├── src/
│   ├── ingestion/           # Log ingestion modules
│   ├── preprocessing/       # Data preprocessing
│   ├── error_classification/ # ML-based error classification
│   ├── root_cause_analysis/ # AI-powered root cause detection
│   ├── anomaly_detection/   # Advanced anomaly detection
│   ├── trend_detection/     # Trend analysis
│   ├── utils/              # Utility functions
│   └── main.py             # Command-line interface
├── ui/                     # Web Interface
│   ├── app.py              # Flask web application
│   ├── templates/          # HTML templates
│   └── README.md           # UI documentation
├── data/
│   ├── raw/                # Raw log files
│   └── processed/          # Processed data
├── models/                 # ML models
├── requirements.txt        # Full dependencies
├── requirements-minimal.txt # Minimal dependencies for UI
└── README.md              # This file
```

## Quick Start

### 🚀 **Option 1: Simple UI (Recommended for Quick Testing)**

If you encounter Python compatibility issues, use the simple UI:

```bash
# Quick start with minimal dependencies
python start_simple_ui.py
```

This will install only Flask and essential packages, then start a simplified but fully functional UI.

### 🔧 **Option 2: Full UI (All Features)**

For the complete experience with all ML features:

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd devops-ai-log-analysis
   ```

2. **Fix compatibility issues (if needed):**
   ```bash
   python fix_compatibility.py
   ```

3. **Start the web interface:**
   ```bash
   python start_ui.py
   ```

4. **Access the interface:**
   Open your browser to `http://localhost:5000`

### 🖥️ **Option 3: Command Line Interface**

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the analysis:**
   ```bash
   python src/main.py
   ```

## Troubleshooting

### Python Version Issues

If you see TensorFlow compatibility errors:

1. **Use the simple UI:**
   ```bash
   python start_simple_ui.py
   ```

2. **Or fix compatibility:**
   ```bash
   python fix_compatibility.py
   ```

3. **Or use minimal requirements:**
   ```bash
   pip install -r requirements-minimal.txt
   ```

### Common Solutions

- **Python 3.13+**: Use `start_simple_ui.py` or `fix_compatibility.py`
- **TensorFlow errors**: Install with `pip install tensorflow>=2.15.0`
- **Package conflicts**: Use `requirements-minimal.txt`
- **Port in use**: Kill process with `lsof -ti:5000 | xargs kill -9`

## Setup Instructions

### Prerequisites
- Python 3.9 or higher (tested with Python 3.13)
- 4GB+ RAM recommended for optimal performance
- Modern web browser for UI

### Installation Options

#### Full Installation (All Features)
```bash
pip install -r requirements.txt
```

#### Minimal Installation (UI Only)
If you encounter compatibility issues:
```bash
pip install -r requirements-minimal.txt
```

### Python Version Compatibility
- **Python 3.9-3.12**: Full compatibility with all features
- **Python 3.13**: Use updated requirements.txt (TensorFlow 2.15+)
- **Python 3.8**: Use requirements-minimal.txt for basic functionality

## Usage

### Web Interface Usage

1. **Start the server:**
   ```bash
   python start_ui.py
   ```

2. **Access the interface:**
   Open `http://localhost:5000` in your browser

3. **Upload and analyze logs:**
   - Upload files via drag-and-drop or file browser
   - View real-time analysis results
   - Explore interactive charts and recommendations
   - Get actionable insights for issue resolution

### Command Line Usage

```bash
# Run full analysis pipeline
python src/main.py

# Create sample data for testing
python create_demo_files.py

# Test the UI functionality
python test_ui.py
```

## Supported Log Formats

- **Text Files** (`.txt`, `.log`): Standard log files with multiline support
- **JSON Files** (`.json`): Structured log data
- **XML Files** (`.xml`): XML-formatted logs
- **CSV Files** (`.csv`): Comma-separated log entries
- **Time Series Data**: Temporal log analysis

## Key Features

### Root Cause Analysis
- AI-powered pattern recognition
- Priority scoring (0-100 scale)
- Actionable recommendations
- Category-based analysis
- Frequency-based insights

### Anomaly Detection
- Statistical anomaly detection
- Machine learning-based detection
- Real-time monitoring
- Severity classification
- Trend analysis

### Error Classification
- Automatic error categorization
- Machine learning models
- Severity assessment
- Pattern matching
- Multiline error support

## Documentation

- **[UI Documentation](ui/README.md)**: Complete web interface guide
- **[Implementation Summary](UI_IMPLEMENTATION_SUMMARY.md)**: Technical details
- **[Enhanced System Summary](ENHANCED_SYSTEM_SUMMARY.md)**: Advanced features
- **[Quick Start Guide](QUICK_START_GUIDE.md)**: Getting started quickly

## Testing

```bash
# Create sample log files
python create_demo_files.py

# Test the web UI
python test_ui.py

# Run analysis tests
python test_advanced_anomaly_detection.py
```

## Contributing

Contributions are welcome! Please submit a pull request or open an issue for any enhancements or bug fixes.

## License

This project is licensed under the MIT License. See the LICENSE file for details.