#!/usr/bin/env python3
"""
DevOps Knowledge Base for Root Cause Analysis
Comprehensive database of common DevOps issues, their causes, and solutions.
"""

import json
import re
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass, asdict
from collections import defaultdict

@dataclass
class KnowledgeEntry:
    """Represents a knowledge base entry with problem, cause, and solution."""
    id: str
    category: str
    problem: str
    symptoms: List[str]
    root_causes: List[str]
    solutions: List[str]
    prevention: List[str]
    keywords: List[str]
    severity: str  # critical, high, medium, low
    frequency: str  # common, uncommon, rare
    tools: List[str]  # Tools that can help diagnose/fix
    related_issues: List[str]  # Related problem IDs
    last_updated: str

class DevOpsKnowledgeBase:
    """Intelligent knowledge base for DevOps root cause analysis."""
    
    def __init__(self):
        self.knowledge_entries: Dict[str, KnowledgeEntry] = {}
        self.category_index: Dict[str, List[str]] = defaultdict(list)
        self.keyword_index: Dict[str, List[str]] = defaultdict(list)
        self.severity_index: Dict[str, List[str]] = defaultdict(list)
        self._initialize_knowledge_base()
    
    def _initialize_knowledge_base(self):
        """Initialize the knowledge base with comprehensive DevOps issues."""
        
        # Database Related Issues
        self._add_database_issues()
        
        # Build and Compilation Issues
        self._add_build_issues()
        
        # Infrastructure Issues
        self._add_infrastructure_issues()
        
        # Code Quality Issues
        self._add_code_quality_issues()
        
        # Security Issues
        self._add_security_issues()
        
        # Performance Issues
        self._add_performance_issues()
        
        # Deployment Issues
        self._add_deployment_issues()
        
        # Monitoring and Observability Issues
        self._add_monitoring_issues()
        
        # Network Issues
        self._add_network_issues()
        
        # Container and Orchestration Issues
        self._add_container_issues()
        
        # Build the indexes
        self._build_indexes()
    
    def _add_database_issues(self):
        """Add database-related issues to the knowledge base."""
        
        # Database Connection Issues
        self.add_entry(KnowledgeEntry(
            id="db_001",
            category="Database",
            problem="Database Connection Timeout",
            symptoms=[
                "Connection timeout errors",
                "Database connection pool exhausted",
                "Application hangs when accessing database",
                "JDBC connection timeout",
                "MySQL connection timeout"
            ],
            root_causes=[
                "Database server overloaded",
                "Network connectivity issues",
                "Connection pool misconfiguration",
                "Database locks/deadlocks",
                "Insufficient database resources"
            ],
            solutions=[
                "Increase connection timeout settings",
                "Optimize database queries",
                "Review and tune connection pool settings",
                "Check database server resources (CPU, memory)",
                "Analyze slow query logs",
                "Implement connection retry logic"
            ],
            prevention=[
                "Monitor database performance metrics",
                "Set up connection pool monitoring",
                "Implement database health checks",
                "Use connection pooling best practices",
                "Regular database maintenance"
            ],
            keywords=["database", "timeout", "connection", "mysql", "postgresql", "jdbc", "pool"],
            severity="high",
            frequency="common",
            tools=["MySQL Workbench", "pgAdmin", "DataDog", "New Relic", "CloudWatch"],
            related_issues=["db_002", "perf_001"],
            last_updated=datetime.now().isoformat()
        ))
        
        # Database Lock Issues
        self.add_entry(KnowledgeEntry(
            id="db_002",
            category="Database",
            problem="Database Deadlock",
            symptoms=[
                "Transaction rollback errors",
                "Lock wait timeout exceeded",
                "Database query hanging",
                "Deadlock detected error messages"
            ],
            root_causes=[
                "Concurrent transactions accessing same resources",
                "Poor transaction design",
                "Long-running transactions",
                "Inadequate database indexing"
            ],
            solutions=[
                "Implement proper transaction isolation",
                "Optimize query execution order",
                "Reduce transaction scope",
                "Add appropriate database indexes",
                "Implement retry logic for deadlocks"
            ],
            prevention=[
                "Design transactions to access resources in consistent order",
                "Keep transactions short",
                "Use appropriate isolation levels",
                "Monitor database locks"
            ],
            keywords=["deadlock", "lock", "transaction", "database", "rollback"],
            severity="high",
            frequency="common",
            tools=["MySQL", "PostgreSQL", "SQL Server Management Studio"],
            related_issues=["db_001", "perf_002"],
            last_updated=datetime.now().isoformat()
        ))
    
    def _add_build_issues(self):
        """Add build and compilation issues."""
        
        # Jenkins Build Failure
        self.add_entry(KnowledgeEntry(
            id="build_001",
            category="Build",
            problem="Jenkins Build Failure",
            symptoms=[
                "Build job fails with exit code 1",
                "Jenkins pipeline stuck",
                "Build timeout",
                "Test failures causing build to fail"
            ],
            root_causes=[
                "Dependency resolution failures",
                "Test environment issues",
                "Insufficient build resources",
                "Code compilation errors",
                "Missing environment variables"
            ],
            solutions=[
                "Check build logs for specific error messages",
                "Verify dependency versions and availability",
                "Increase build timeout settings",
                "Allocate more resources to build agents",
                "Fix failing tests or compilation errors"
            ],
            prevention=[
                "Use dependency management tools",
                "Implement build health monitoring",
                "Use containerized build environments",
                "Regular build agent maintenance"
            ],
            keywords=["jenkins", "build", "failure", "pipeline", "compilation"],
            severity="high",
            frequency="common",
            tools=["Jenkins", "Maven", "Gradle", "Docker", "SonarQube"],
            related_issues=["build_002", "test_001"],
            last_updated=datetime.now().isoformat()
        ))
        
        # Compilation Errors
        self.add_entry(KnowledgeEntry(
            id="build_002",
            category="Build",
            problem="Compilation Errors",
            symptoms=[
                "Compiler error messages",
                "Missing class/module errors",
                "Syntax errors",
                "Import/include errors"
            ],
            root_causes=[
                "Syntax errors in source code",
                "Missing dependencies",
                "Incorrect classpath/module path",
                "Version compatibility issues"
            ],
            solutions=[
                "Review and fix syntax errors",
                "Verify all dependencies are available",
                "Check classpath configuration",
                "Update incompatible library versions",
                "Clean and rebuild project"
            ],
            prevention=[
                "Use IDE with real-time syntax checking",
                "Implement pre-commit hooks",
                "Use dependency management tools",
                "Regular code reviews"
            ],
            keywords=["compilation", "compiler", "syntax", "error", "dependency"],
            severity="medium",
            frequency="common",
            tools=["IDE", "Maven", "Gradle", "NPM", "Pip"],
            related_issues=["build_001", "dep_001"],
            last_updated=datetime.now().isoformat()
        ))
    
    def _add_infrastructure_issues(self):
        """Add infrastructure-related issues."""
        
        # Disk Space Issues
        self.add_entry(KnowledgeEntry(
            id="infra_001",
            category="Infrastructure",
            problem="Disk Space Full",
            symptoms=[
                "No space left on device error",
                "Application crashes due to disk full",
                "Log files not being written",
                "Database write failures"
            ],
            root_causes=[
                "Log files consuming excessive space",
                "Application data growth",
                "Temporary files not cleaned up",
                "Database files growing rapidly"
            ],
            solutions=[
                "Clean up old log files",
                "Implement log rotation",
                "Remove unnecessary temporary files",
                "Archive old data",
                "Add more disk space",
                "Move data to external storage"
            ],
            prevention=[
                "Set up disk space monitoring",
                "Implement automatic log rotation",
                "Regular cleanup of temporary files",
                "Monitor application data growth"
            ],
            keywords=["disk", "space", "full", "storage", "cleanup"],
            severity="critical",
            frequency="common",
            tools=["df", "du", "logrotate", "CloudWatch", "Nagios"],
            related_issues=["infra_002", "log_001"],
            last_updated=datetime.now().isoformat()
        ))
        
        # Memory Issues
        self.add_entry(KnowledgeEntry(
            id="infra_002",
            category="Infrastructure",
            problem="Insufficient Memory",
            symptoms=[
                "Out of memory errors",
                "Application crashes",
                "System becomes unresponsive",
                "Swap usage high"
            ],
            root_causes=[
                "Memory leaks in application",
                "Insufficient RAM allocation",
                "Memory-intensive operations",
                "Too many concurrent processes"
            ],
            solutions=[
                "Identify and fix memory leaks",
                "Increase available RAM",
                "Optimize memory usage in applications",
                "Implement memory monitoring",
                "Use memory profiling tools"
            ],
            prevention=[
                "Regular memory monitoring",
                "Memory leak testing",
                "Proper resource management in code",
                "Capacity planning"
            ],
            keywords=["memory", "ram", "oom", "leak", "insufficient"],
            severity="critical",
            frequency="common",
            tools=["top", "htop", "valgrind", "JProfiler", "New Relic"],
            related_issues=["infra_001", "perf_001"],
            last_updated=datetime.now().isoformat()
        ))
        
        # CPU High Usage
        self.add_entry(KnowledgeEntry(
            id="infra_003",
            category="Infrastructure",
            problem="High CPU Usage",
            symptoms=[
                "System slowdown",
                "Application response time increased",
                "CPU usage above 80%",
                "Load average high"
            ],
            root_causes=[
                "Inefficient algorithms",
                "Infinite loops or recursive calls",
                "Too many concurrent processes",
                "Resource-intensive operations"
            ],
            solutions=[
                "Identify CPU-intensive processes",
                "Optimize algorithms and code",
                "Implement caching",
                "Scale horizontally",
                "Use profiling tools to identify bottlenecks"
            ],
            prevention=[
                "Performance testing",
                "Code reviews for efficiency",
                "CPU monitoring and alerting",
                "Capacity planning"
            ],
            keywords=["cpu", "high", "usage", "performance", "load"],
            severity="high",
            frequency="common",
            tools=["top", "htop", "perf", "JProfiler", "APM tools"],
            related_issues=["perf_001", "infra_002"],
            last_updated=datetime.now().isoformat()
        ))
    
    def _add_code_quality_issues(self):
        """Add code quality and analysis issues."""
        
        # SonarQube Issues
        self.add_entry(KnowledgeEntry(
            id="quality_001",
            category="Code Quality",
            problem="SonarQube Quality Gate Failure",
            symptoms=[
                "Quality gate failing",
                "High technical debt",
                "Code coverage below threshold",
                "Security vulnerabilities detected"
            ],
            root_causes=[
                "Poor code quality",
                "Insufficient test coverage",
                "Security vulnerabilities in code",
                "Code duplication"
            ],
            solutions=[
                "Fix code smells and bugs",
                "Increase test coverage",
                "Address security vulnerabilities",
                "Refactor duplicate code",
                "Review and adjust quality gate rules"
            ],
            prevention=[
                "Implement code reviews",
                "Use static analysis tools",
                "Test-driven development",
                "Regular code quality monitoring"
            ],
            keywords=["sonarqube", "quality", "gate", "coverage", "security"],
            severity="medium",
            frequency="common",
            tools=["SonarQube", "SonarLint", "JaCoCo", "ESLint"],
            related_issues=["test_001", "security_001"],
            last_updated=datetime.now().isoformat()
        ))
        
        # Code Coverage Issues
        self.add_entry(KnowledgeEntry(
            id="test_001",
            category="Testing",
            problem="Low Code Coverage",
            symptoms=[
                "Code coverage below target",
                "Build fails on coverage check",
                "Untested code paths",
                "Missing test cases"
            ],
            root_causes=[
                "Insufficient unit tests",
                "Missing integration tests",
                "Code added without tests",
                "Test exclusions too broad"
            ],
            solutions=[
                "Write unit tests for uncovered code",
                "Add integration tests",
                "Review test exclusions",
                "Implement test-driven development",
                "Use coverage tools to identify gaps"
            ],
            prevention=[
                "Enforce coverage requirements",
                "Code review process",
                "Test-driven development",
                "Regular coverage monitoring"
            ],
            keywords=["coverage", "test", "unit", "integration", "jacoco"],
            severity="medium",
            frequency="common",
            tools=["JaCoCo", "Istanbul", "Coverage.py", "SonarQube"],
            related_issues=["quality_001", "build_001"],
            last_updated=datetime.now().isoformat()
        ))
    
    def _add_security_issues(self):
        """Add security-related issues."""
        
        # Authentication Failures
        self.add_entry(KnowledgeEntry(
            id="security_001",
            category="Security",
            problem="Authentication Failures",
            symptoms=[
                "401 Unauthorized errors",
                "Failed login attempts",
                "Token validation failures",
                "Session timeout errors"
            ],
            root_causes=[
                "Invalid credentials",
                "Expired tokens/sessions",
                "Misconfigured authentication",
                "Network connectivity issues"
            ],
            solutions=[
                "Verify credential configuration",
                "Implement token refresh mechanism",
                "Check authentication service status",
                "Review authentication flow",
                "Implement proper error handling"
            ],
            prevention=[
                "Use secure authentication methods",
                "Monitor authentication metrics",
                "Implement rate limiting",
                "Regular security audits"
            ],
            keywords=["authentication", "401", "unauthorized", "login", "token"],
            severity="high",
            frequency="common",
            tools=["OAuth", "JWT", "LDAP", "Active Directory"],
            related_issues=["security_002", "network_001"],
            last_updated=datetime.now().isoformat()
        ))
        
        # SSL/TLS Issues
        self.add_entry(KnowledgeEntry(
            id="security_002",
            category="Security",
            problem="SSL/TLS Certificate Issues",
            symptoms=[
                "Certificate validation errors",
                "SSL handshake failures",
                "Certificate expired warnings",
                "Untrusted certificate errors"
            ],
            root_causes=[
                "Expired certificates",
                "Invalid certificate chain",
                "Hostname mismatch",
                "Self-signed certificates"
            ],
            solutions=[
                "Renew expired certificates",
                "Fix certificate chain issues",
                "Ensure hostname matches certificate",
                "Use trusted certificate authority",
                "Update certificate stores"
            ],
            prevention=[
                "Monitor certificate expiration",
                "Automate certificate renewal",
                "Use certificate management tools",
                "Regular security scans"
            ],
            keywords=["ssl", "tls", "certificate", "expired", "handshake"],
            severity="high",
            frequency="common",
            tools=["OpenSSL", "Let's Encrypt", "Certificate Manager"],
            related_issues=["security_001", "network_002"],
            last_updated=datetime.now().isoformat()
        ))
    
    def _add_performance_issues(self):
        """Add performance-related issues."""
        
        # Slow Response Times
        self.add_entry(KnowledgeEntry(
            id="perf_001",
            category="Performance",
            problem="Slow API Response Times",
            symptoms=[
                "High response times",
                "Timeout errors",
                "User complaints about slowness",
                "Application unresponsive"
            ],
            root_causes=[
                "Database query optimization needed",
                "Network latency",
                "Insufficient server resources",
                "Memory leaks",
                "Blocking operations"
            ],
            solutions=[
                "Optimize database queries",
                "Implement caching",
                "Scale server resources",
                "Profile and fix memory leaks",
                "Use asynchronous operations"
            ],
            prevention=[
                "Performance monitoring",
                "Load testing",
                "Database query analysis",
                "Resource monitoring"
            ],
            keywords=["performance", "slow", "response", "timeout", "latency"],
            severity="high",
            frequency="common",
            tools=["APM tools", "New Relic", "DataDog", "JProfiler"],
            related_issues=["db_001", "infra_002"],
            last_updated=datetime.now().isoformat()
        ))
    
    def _add_deployment_issues(self):
        """Add deployment-related issues."""
        
        # Deployment Failures
        self.add_entry(KnowledgeEntry(
            id="deploy_001",
            category="Deployment",
            problem="Deployment Failure",
            symptoms=[
                "Deployment scripts failing",
                "Application not starting after deployment",
                "Configuration errors",
                "Database migration failures"
            ],
            root_causes=[
                "Configuration mismatches",
                "Database schema changes",
                "Dependency conflicts",
                "Environment differences"
            ],
            solutions=[
                "Verify configuration files",
                "Test database migrations",
                "Check dependency versions",
                "Use infrastructure as code",
                "Implement blue-green deployment"
            ],
            prevention=[
                "Automated deployment testing",
                "Environment parity",
                "Configuration management",
                "Rollback procedures"
            ],
            keywords=["deployment", "failure", "configuration", "migration"],
            severity="high",
            frequency="common",
            tools=["Ansible", "Terraform", "Kubernetes", "Docker"],
            related_issues=["config_001", "db_003"],
            last_updated=datetime.now().isoformat()
        ))
    
    def _add_monitoring_issues(self):
        """Add monitoring and observability issues."""
        
        # Log Analysis Issues
        self.add_entry(KnowledgeEntry(
            id="log_001",
            category="Monitoring",
            problem="Log Analysis Difficulties",
            symptoms=[
                "Cannot find relevant logs",
                "Log files too large",
                "Inconsistent log formats",
                "Missing log entries"
            ],
            root_causes=[
                "Poor log management",
                "Inadequate log rotation",
                "Inconsistent logging practices",
                "Log level misconfiguration"
            ],
            solutions=[
                "Implement centralized logging",
                "Standardize log formats",
                "Configure proper log rotation",
                "Use structured logging",
                "Implement log aggregation"
            ],
            prevention=[
                "Logging best practices",
                "Regular log review",
                "Monitoring log volumes",
                "Automated log analysis"
            ],
            keywords=["log", "logging", "analysis", "format", "rotation"],
            severity="medium",
            frequency="common",
            tools=["ELK Stack", "Splunk", "Fluentd", "Logstash"],
            related_issues=["infra_001", "monitor_001"],
            last_updated=datetime.now().isoformat()
        ))
    
    def _add_network_issues(self):
        """Add network-related issues."""
        
        # Network Connectivity
        self.add_entry(KnowledgeEntry(
            id="network_001",
            category="Network",
            problem="Network Connectivity Issues",
            symptoms=[
                "Connection refused errors",
                "Network timeout",
                "DNS resolution failures",
                "Intermittent connectivity"
            ],
            root_causes=[
                "Network configuration issues",
                "Firewall blocking connections",
                "DNS misconfiguration",
                "Network hardware problems"
            ],
            solutions=[
                "Check network configuration",
                "Verify firewall rules",
                "Test DNS resolution",
                "Monitor network hardware",
                "Use network diagnostic tools"
            ],
            prevention=[
                "Network monitoring",
                "Regular network audits",
                "Redundant network paths",
                "Network documentation"
            ],
            keywords=["network", "connectivity", "timeout", "dns", "firewall"],
            severity="high",
            frequency="common",
            tools=["ping", "traceroute", "nslookup", "Wireshark"],
            related_issues=["security_001", "infra_004"],
            last_updated=datetime.now().isoformat()
        ))
    
    def _add_container_issues(self):
        """Add container and orchestration issues."""
        
        # Docker Issues
        self.add_entry(KnowledgeEntry(
            id="container_001",
            category="Container",
            problem="Docker Container Startup Failure",
            symptoms=[
                "Container exits immediately",
                "Application not accessible",
                "Port binding failures",
                "Volume mount errors"
            ],
            root_causes=[
                "Application configuration errors",
                "Resource constraints",
                "Port conflicts",
                "Volume permission issues"
            ],
            solutions=[
                "Check application logs",
                "Verify resource limits",
                "Resolve port conflicts",
                "Fix volume permissions",
                "Update container image"
            ],
            prevention=[
                "Container health checks",
                "Resource monitoring",
                "Image vulnerability scanning",
                "Regular updates"
            ],
            keywords=["docker", "container", "startup", "failure", "port"],
            severity="high",
            frequency="common",
            tools=["Docker", "Kubernetes", "Podman", "containerd"],
            related_issues=["k8s_001", "infra_002"],
            last_updated=datetime.now().isoformat()
        ))
        
        # Kubernetes Issues
        self.add_entry(KnowledgeEntry(
            id="k8s_001",
            category="Kubernetes",
            problem="Pod CrashLoopBackOff",
            symptoms=[
                "Pod keeps restarting",
                "CrashLoopBackOff status",
                "Application unavailable",
                "Restart count increasing"
            ],
            root_causes=[
                "Application startup failures",
                "Resource limits exceeded",
                "Configuration errors",
                "Health check failures"
            ],
            solutions=[
                "Check pod logs",
                "Verify resource requests/limits",
                "Fix configuration issues",
                "Update health check probes",
                "Debug application startup"
            ],
            prevention=[
                "Proper resource planning",
                "Application monitoring",
                "Regular health checks",
                "Configuration validation"
            ],
            keywords=["kubernetes", "pod", "crashloopbackoff", "restart"],
            severity="high",
            frequency="common",
            tools=["kubectl", "Kubernetes Dashboard", "Helm"],
            related_issues=["container_001", "monitor_001"],
            last_updated=datetime.now().isoformat()
        ))
    
    def add_entry(self, entry: KnowledgeEntry):
        """Add a new knowledge entry to the database."""
        self.knowledge_entries[entry.id] = entry
    
    def _build_indexes(self):
        """Build search indexes for efficient lookups."""
        self.category_index.clear()
        self.keyword_index.clear()
        self.severity_index.clear()
        
        for entry_id, entry in self.knowledge_entries.items():
            # Category index
            self.category_index[entry.category.lower()].append(entry_id)
            
            # Keyword index
            for keyword in entry.keywords:
                self.keyword_index[keyword.lower()].append(entry_id)
            
            # Severity index
            self.severity_index[entry.severity.lower()].append(entry_id)
    
    def search_by_symptoms(self, symptoms: List[str]) -> List[KnowledgeEntry]:
        """Search for knowledge entries by symptoms."""
        matches = []
        
        for entry in self.knowledge_entries.values():
            score = 0
            for symptom in symptoms:
                for entry_symptom in entry.symptoms:
                    if any(word in entry_symptom.lower() for word in symptom.lower().split()):
                        score += 1
            
            if score > 0:
                matches.append((entry, score))
        
        # Sort by relevance score
        matches.sort(key=lambda x: x[1], reverse=True)
        return [match[0] for match in matches[:10]]  # Return top 10
    
    def search_by_keywords(self, keywords: List[str]) -> List[KnowledgeEntry]:
        """Search for knowledge entries by keywords."""
        matching_ids = set()
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            if keyword_lower in self.keyword_index:
                matching_ids.update(self.keyword_index[keyword_lower])
        
        return [self.knowledge_entries[entry_id] for entry_id in matching_ids]
    
    def search_by_category(self, category: str) -> List[KnowledgeEntry]:
        """Search for knowledge entries by category."""
        category_lower = category.lower()
        if category_lower in self.category_index:
            return [self.knowledge_entries[entry_id] for entry_id in self.category_index[category_lower]]
        return []
    
    def search_by_severity(self, severity: str) -> List[KnowledgeEntry]:
        """Search for knowledge entries by severity."""
        severity_lower = severity.lower()
        if severity_lower in self.severity_index:
            return [self.knowledge_entries[entry_id] for entry_id in self.severity_index[severity_lower]]
        return []
    
    def get_recommendations(self, error_message: str, category: str = None) -> List[Dict[str, Any]]:
        """Get recommendations based on error message and optional category."""
        recommendations = []
        
        # Extract keywords from error message
        keywords = self._extract_keywords(error_message)
        
        # Search by keywords
        keyword_matches = self.search_by_keywords(keywords)
        
        # Search by symptoms
        symptom_matches = self.search_by_symptoms([error_message])
        
        # Combine and deduplicate
        all_matches = list(set(keyword_matches + symptom_matches))
        
        # Filter by category if specified
        if category:
            all_matches = [match for match in all_matches if match.category.lower() == category.lower()]
        
        # Convert to recommendation format
        for match in all_matches[:5]:  # Top 5 recommendations
            recommendation = {
                'id': match.id,
                'category': match.category,
                'problem': match.problem,
                'relevance_score': self._calculate_relevance(error_message, match),
                'solutions': match.solutions,
                'prevention': match.prevention,
                'tools': match.tools,
                'severity': match.severity,
                'related_issues': match.related_issues
            }
            recommendations.append(recommendation)
        
        # Sort by relevance score
        recommendations.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        return recommendations
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract relevant keywords from text."""
        # Common DevOps keywords
        devops_keywords = [
            'database', 'mysql', 'postgresql', 'connection', 'timeout', 'deadlock',
            'build', 'jenkins', 'compilation', 'maven', 'gradle', 'npm',
            'docker', 'kubernetes', 'container', 'pod', 'deployment',
            'memory', 'cpu', 'disk', 'space', 'performance', 'slow',
            'ssl', 'tls', 'certificate', 'authentication', 'security',
            'network', 'connectivity', 'firewall', 'dns',
            'log', 'monitoring', 'alert', 'error', 'exception'
        ]
        
        text_lower = text.lower()
        found_keywords = []
        
        for keyword in devops_keywords:
            if keyword in text_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _calculate_relevance(self, error_message: str, entry: KnowledgeEntry) -> float:
        """Calculate relevance score between error message and knowledge entry."""
        score = 0.0
        error_lower = error_message.lower()
        
        # Check symptoms
        for symptom in entry.symptoms:
            if any(word in error_lower for word in symptom.lower().split()):
                score += 2.0
        
        # Check keywords
        for keyword in entry.keywords:
            if keyword in error_lower:
                score += 1.0
        
        # Check problem description
        if any(word in error_lower for word in entry.problem.lower().split()):
            score += 1.5
        
        return score
    
    def get_categories(self) -> List[str]:
        """Get all available categories."""
        return list(self.category_index.keys())
    
    def get_entry_by_id(self, entry_id: str) -> Optional[KnowledgeEntry]:
        """Get a specific knowledge entry by ID."""
        return self.knowledge_entries.get(entry_id)
    
    def export_knowledge_base(self, filename: str):
        """Export knowledge base to JSON file."""
        data = {
            'entries': [asdict(entry) for entry in self.knowledge_entries.values()],
            'export_date': datetime.now().isoformat()
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
    
    def import_knowledge_base(self, filename: str):
        """Import knowledge base from JSON file."""
        with open(filename, 'r') as f:
            data = json.load(f)
        
        for entry_data in data['entries']:
            entry = KnowledgeEntry(**entry_data)
            self.add_entry(entry)
        
        self._build_indexes()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get knowledge base statistics."""
        stats = {
            'total_entries': len(self.knowledge_entries),
            'categories': {},
            'severities': {},
            'most_common_keywords': {}
        }
        
        # Category statistics
        for category, entries in self.category_index.items():
            stats['categories'][category] = len(entries)
        
        # Severity statistics
        for severity, entries in self.severity_index.items():
            stats['severities'][severity] = len(entries)
        
        # Keyword statistics
        keyword_counts = defaultdict(int)
        for entry in self.knowledge_entries.values():
            for keyword in entry.keywords:
                keyword_counts[keyword] += 1
        
        # Top 10 keywords
        top_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        stats['most_common_keywords'] = dict(top_keywords)
        
        return stats

# Global knowledge base instance
knowledge_base = DevOpsKnowledgeBase()
