"""
Natural Language Query Processor for DevOps AI Log Analysis
Converts natural language queries into structured log searches.
"""

import json
import re
import os
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import logging
from dotenv import load_dotenv
import base64
from openai import AzureOpenAI
import requests

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NaturalLanguageQueryProcessor:
    """
    Process natural language queries and convert them to structured log queries.
    Works with or without OpenAI API - provides pattern-based fallback.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the query processor.
        
        Args:
            api_key: OpenAI API key (optional - will try environment variable)
        """
        # Generate API key using Cisco-specific endpoint
        client_id = os.getenv('CISCO_CLIENT_ID')
        client_secret = os.getenv('CISCO_CLIENT_SECRET')
        url = "https://id.cisco.com/oauth2/default/v1/token"
        payload = "grant_type=client_credentials"
        value = base64.b64encode(f'{client_id}:{client_secret}'.encode('utf-8')).decode('utf-8')
        headers = {
            "Accept": "*/*",
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": f"Basic {value}"
        }

        try:
            token_response = requests.request("POST", url, headers=headers, data=payload)
            token_response.raise_for_status()
            self.api_key = token_response.json()["access_token"]
        except Exception as e:
            logger.error(f"Failed to fetch API key: {e}")
            self.api_key = None

        self.use_openai = bool(self.api_key)

        if self.use_openai:
            try:
                self.client = AzureOpenAI(
                    azure_endpoint='https://chat-ai.cisco.com',
                    api_key=self.api_key,
                    api_version="2023-08-01-preview"
                )
                logger.info("Cisco-specific OpenAI integration enabled")
            except Exception as e:
                logger.warning(f"AzureOpenAI initialization failed: {e}. Using pattern-based processing only.")
                self.use_openai = False
        else:
            logger.info("OpenAI API key not provided. Using pattern-based processing only.")
        
        # Fetch the appkey from the environment
        self.appkey = os.getenv('CISCO_APP_KEY')
        if not self.appkey:
            logger.error("CISCO_APP_KEY is not set in the environment. LLM features will be disabled.")
            self.use_openai = False

        # Test the API key with a simple OpenAI API call
        if self.use_openai:
            try:
                test_response = self.client.chat.completions.create(
                        model="gpt-4o",
                        messages=[{"role": "system", "content": "Test API key"}],
                        user=json.dumps({"appkey": self.appkey})
                    )
                logger.info("OpenAI API key is valid. Test response received.")
            except Exception as e:
                logger.error(f"OpenAI API key validation failed: {e}")
                self.use_openai = False

        # Pattern definitions for regex-based extraction
        self.query_patterns = {
            'time_range': r'(?:in the )?(?:last|past|within) (\d+) (minute|hour|day|week|month)s?',
            'service': r'(?:from|in|for) (?:service|app|application|component) (\w+)',
            'error_type': r'(error|exception|failure|crash|timeout|connection|auth|login|database|db)',
            'severity': r'(critical|error|warning|info|debug|fatal|severe)',
            'user_action': r'(login|logout|authentication|authorization|access|signup|register)',
            'count': r'(?:show|find|get) (?:me )?(?:all|top|first) (\d+)',
            'status': r'(failed|successful|success|fail|denied|blocked|allowed)',
        }
        
        # Common query templates
        self.query_templates = {
            'error_search': {
                'patterns': ['error', 'exception', 'failure', 'crash'],
                'description': 'Find error-related log entries'
            },
            'auth_search': {
                'patterns': ['auth', 'login', 'authentication', 'authorization'],
                'description': 'Find authentication-related log entries'
            },
            'service_search': {
                'patterns': ['service', 'app', 'application'],
                'description': 'Find logs from specific services'
            },
            'performance_search': {
                'patterns': ['slow', 'timeout', 'performance', 'latency'],
                'description': 'Find performance-related issues'
            }
        }
    
    def process_query(self, query: str) -> Dict[str, Any]:
        """
        Convert natural language query to structured search parameters.
        
        Args:
            query: Natural language query string
            
        Returns:
            Dict containing structured search parameters
        """
        logger.info(f"Processing query: {query}")
        
        # Extract basic patterns using regex
        patterns = self._extract_patterns(query)
        
        # Retrieve historical context
        historical_context = self._retrieve_historical_context(query)
        
        # Use LLM analysis if available
        llm_analysis = {}
        if self.use_openai:
            try:
                llm_analysis = self._analyze_with_llm(query)
            except Exception as e:
                logger.warning(f"LLM analysis failed: {e}")
        
        # Generate search filters
        search_filters = self._generate_search_filters(query, patterns, llm_analysis)
        
        # Create structured search parameters
        search_params = {
            'original_query': query,
            'time_range': patterns.get('time_range'),
            'service': patterns.get('service'),
            'error_types': patterns.get('error_types', []),
            'severity': patterns.get('severity'),
            'status': patterns.get('status'),
            'count_limit': patterns.get('count_limit', 50),
            'search_filters': search_filters,
            'keywords': self._extract_keywords(query),
            'query_type': self._determine_query_type(query),
            'confidence': self._calculate_confidence(patterns, llm_analysis),
            'llm_analysis': llm_analysis if llm_analysis else None,
            'historical_context': historical_context,
            'processing_method': 'llm' if self.use_openai and llm_analysis else 'pattern'
        }
        
        logger.info(f"Generated search params with {search_params['confidence']:.2f} confidence")
        return search_params
    
    def _extract_patterns(self, query: str) -> Dict[str, Any]:
        """Extract basic patterns using regex."""
        patterns = {}
        query_lower = query.lower()
        
        # Time range extraction
        time_match = re.search(self.query_patterns['time_range'], query_lower)
        if time_match:
            amount, unit = time_match.groups()
            patterns['time_range'] = self._calculate_time_range(int(amount), unit)
        
        # Service extraction
        service_match = re.search(self.query_patterns['service'], query_lower)
        if service_match:
            patterns['service'] = service_match.group(1)
        
        # Error type extraction
        error_matches = re.findall(self.query_patterns['error_type'], query_lower)
        if error_matches:
            patterns['error_types'] = list(set(error_matches))
        
        # Severity extraction
        severity_match = re.search(self.query_patterns['severity'], query_lower)
        if severity_match:
            patterns['severity'] = severity_match.group(1).upper()
        
        # Status extraction
        status_match = re.search(self.query_patterns['status'], query_lower)
        if status_match:
            patterns['status'] = status_match.group(1)
        
        # Count limit extraction
        count_match = re.search(self.query_patterns['count'], query_lower)
        if count_match:
            patterns['count_limit'] = int(count_match.group(1))
        
        return patterns
    
    def _analyze_with_llm(self, query: str) -> Dict[str, Any]:
        """Use LLM to analyze complex queries."""
        if not self.use_openai:
            logger.warning("LLM is not enabled. Falling back to pattern-based analysis.")
            return {}

        system_prompt = """
        You are a log analysis expert. Convert natural language queries into structured search parameters for DevOps log analysis.
        
        Return a JSON object with:
        - intent: what the user is trying to find (error_analysis, performance_monitoring, security_audit, etc.)
        - keywords: array of important keywords to search for
        - filters: object with key-value pairs for filtering
        - time_sensitivity: boolean indicating if time is important
        - priority: high/medium/low based on severity indicators
        - explanation: brief explanation of what will be searched
        - suggested_actions: array of follow-up actions if issues are found
        
        Example input: "Show me all authentication failures from user-service in the last hour"
        Example output: {
            "intent": "security_audit",
            "keywords": ["authentication", "failure", "fail", "denied", "error"],
            "filters": {
                "service": "user-service",
                "event_type": "authentication",
                "status": "failure",
                "time_range": "1h"
            },
            "time_sensitivity": true,
            "priority": "high",
            "explanation": "Searching for authentication failures in user-service from the last hour - potential security issue",
            "suggested_actions": ["Check for brute force attempts", "Review user accounts", "Check IP sources"]
        }
        """

        try:
            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Query: {query}"}
                ],
                temperature=0.1,
                max_tokens=500,
                user=json.dumps({"appkey": self.appkey})
            )

            content = response.choices[0].message.content

            # Extract JSON from response
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    logger.warning("Failed to parse LLM JSON response")

        except Exception as e:
            logger.error(f"LLM analysis failed: {e}")

        return {}
    
    def _generate_search_filters(self, query: str, patterns: Dict, llm_analysis: Dict) -> Dict[str, Any]:
        """Generate search filters based on extracted patterns and LLM analysis."""
        filters = {}
        
        # Add pattern-based filters
        if patterns.get('service'):
            filters['service'] = patterns['service']
        
        if patterns.get('severity'):
            filters['severity'] = patterns['severity']
        
        if patterns.get('error_types'):
            filters['error_types'] = patterns['error_types']
        
        if patterns.get('status'):
            filters['status'] = patterns['status']
        
        # Add LLM-based filters if available
        if llm_analysis and 'filters' in llm_analysis:
            filters.update(llm_analysis['filters'])
        
        return filters
    
    def _extract_keywords(self, query: str) -> List[str]:
        """Extract important keywords from the query."""
        # Remove common stop words
        stop_words = {'the', 'and', 'or', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'show', 'me', 'find', 'get', 'all'}
        
        # Extract words
        words = re.findall(r'\b\w+\b', query.lower())
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        return keywords
    
    def _determine_query_type(self, query: str) -> str:
        """Determine the type of query based on keywords."""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['error', 'exception', 'failure', 'crash']):
            return 'error_analysis'
        elif any(word in query_lower for word in ['auth', 'login', 'access', 'permission']):
            return 'security_audit'
        elif any(word in query_lower for word in ['slow', 'timeout', 'performance', 'latency']):
            return 'performance_monitoring'
        elif any(word in query_lower for word in ['service', 'app', 'component']):
            return 'service_monitoring'
        else:
            return 'general_search'
    
    def _calculate_confidence(self, patterns: Dict, llm_analysis: Dict) -> float:
        """Calculate confidence score for the query interpretation."""
        confidence = 0.0
        
        # Pattern-based confidence
        if patterns.get('time_range'):
            confidence += 0.2
        if patterns.get('service'):
            confidence += 0.2
        if patterns.get('error_types'):
            confidence += 0.2
        if patterns.get('severity'):
            confidence += 0.1
        
        # LLM-based confidence boost
        if llm_analysis:
            confidence += 0.3
        
        return min(confidence, 1.0)
    
    def _calculate_time_range(self, amount: int, unit: str) -> Tuple[datetime, datetime]:
        """Calculate datetime range from amount and unit."""
        now = datetime.now()
        
        if unit.lower().startswith('minute'):
            start_time = now - timedelta(minutes=amount)
        elif unit.lower().startswith('hour'):
            start_time = now - timedelta(hours=amount)
        elif unit.lower().startswith('day'):
            start_time = now - timedelta(days=amount)
        elif unit.lower().startswith('week'):
            start_time = now - timedelta(weeks=amount)
        elif unit.lower().startswith('month'):
            start_time = now - timedelta(days=amount * 30)  # Approximate
        else:
            start_time = now - timedelta(hours=1)  # Default to 1 hour
        
        return start_time, now
    
    def _retrieve_historical_context(self, query: str) -> Dict[str, Any]:
        """Retrieve historical context for the given query."""
        logger.info("Retrieving historical context...")
        # Placeholder for historical data retrieval logic
        # In a real implementation, this could query a database or read from a file
        historical_data = {
            "recurring_errors": [
                {"error": "Database connection timeout", "frequency": 5},
                {"error": "Service unavailable", "frequency": 3}
            ],
            "common_resolutions": [
                {"error": "Database connection timeout", "resolution": "Increase connection pool size"},
                {"error": "Service unavailable", "resolution": "Scale up service instances"}
            ]
        }
        return historical_data

    def get_query_suggestions(self, partial_query: str = "") -> List[str]:
        """Get query suggestions based on common patterns."""
        suggestions = [
            "Show me all errors in the last hour",
            "Find authentication failures from user-service",
            "What happened in the payment system today?",
            "Show me critical errors in the database",
            "Find all timeouts in the API gateway",
            "Show me failed login attempts in the last 30 minutes",
            "Find all exceptions in the order processing service",
            "What performance issues occurred yesterday?",
            "Show me all security alerts this week",
            "Find database connection errors in the last 24 hours"
        ]
        
        if partial_query:
            # Filter suggestions based on partial query
            partial_lower = partial_query.lower()
            filtered = [s for s in suggestions if any(word in s.lower() for word in partial_lower.split())]
            return filtered[:5]
        
        return suggestions[:5]
    
    def generate_log_recommendations(self, error_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate AI-powered recommendations for log analysis."""
        if not self.use_openai:
            return []
        
        system_prompt = """
        You are a DevOps expert specializing in log analysis and root cause analysis. 
        Based on the provided log errors and patterns, generate detailed technical recommendations.
        
        Return a JSON array of recommendations with this exact structure:
        [
          {
            "problem": "Brief description of the issue",
            "category": "Technical category (e.g., Database, Network, Security, Performance, Infrastructure)",
            "root_causes": ["List of possible root causes"],
            "solutions": ["List of specific technical solutions"],
            "prevention": ["List of prevention measures"],
            "severity": "high|medium|low",
            "tools": ["List of relevant tools for troubleshooting"],
            "symptoms": ["List of symptoms to look for"],
            "documentation_links": ["List of relevant documentation URLs"],
            "relevance_score": 8.5
          }
        ]
        
        Focus on actionable, specific recommendations. Each recommendation should be technically detailed and practical.
        """
        
        user_prompt = f"""Based on these log errors and patterns, provide 2-3 specific technical recommendations:

Errors:
{chr(10).join(error_context.get('errors', []))}

Detected Technologies: {', '.join(error_context.get('technologies', []))}
Severity: {error_context.get('severity', 'unknown')}

Error Patterns: {error_context.get('patterns', {})}

Please provide detailed technical recommendations in the JSON format specified."""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=1500,
                user=json.dumps({"appkey": self.appkey})
            )
            
            content = response.choices[0].message.content
            
            # Extract JSON from response
            json_match = re.search(r'\[.*\]', content, re.DOTALL)
            if json_match:
                try:
                    recommendations = json.loads(json_match.group())
                    if isinstance(recommendations, list):
                        return recommendations
                    else:
                        return [recommendations]  # Wrap single recommendation in list
                except json.JSONDecodeError:
                    logger.warning("Failed to parse LLM JSON response for recommendations")
            
        except Exception as e:
            logger.error(f"LLM recommendation generation failed: {e}")
        
        return []
    
    def enhance_recommendation(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance a knowledge base recommendation with additional details using LLM."""
        if not self.use_openai:
            return None
        
        original_rec = context.get('original_recommendation', {})
        problem = context.get('problem', 'Unknown Issue')
        category = context.get('category', 'General')
        solutions = context.get('solutions', [])
        root_causes = context.get('root_causes', [])
        errors = context.get('errors', [])
        
        system_prompt = """
        You are a DevOps expert. Enhance the given knowledge base recommendation with additional context, 
        detailed explanations, and practical implementation steps to improve user experience.
        
        Return a JSON object with enhanced recommendation:
        {
            "problem": "Enhanced problem description with more context",
            "category": "Technical category",
            "root_causes": ["Enhanced list of root causes with detailed explanations"],
            "solutions": ["Enhanced solutions with step-by-step implementation"],
            "prevention": ["Enhanced prevention measures with specific actions"],
            "severity": "high|medium|low",
            "tools": ["List of specific tools and commands"],
            "symptoms": ["Detailed symptoms with examples"],
            "documentation_links": ["Relevant documentation URLs"],
            "implementation_notes": ["Practical implementation tips and warnings"],
            "estimated_time": "Estimated time to implement solutions",
            "difficulty_level": "easy|medium|hard",
            "relevance_score": 8.5
        }
        
        Make the recommendation more actionable and user-friendly while keeping technical accuracy.
        """
        
        user_prompt = f"""Enhance this knowledge base recommendation with more details and context:

Original Problem: {problem}
Category: {category}
Current Solutions: {solutions}
Current Root Causes: {root_causes}
Related Errors: {errors}

Please provide an enhanced version that:
1. Explains the problem in more detail
2. Provides step-by-step implementation guidance
3. Includes specific tools and commands
4. Adds practical tips and warnings
5. Estimates implementation time and difficulty

Keep the technical accuracy while making it more user-friendly."""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=1000,
                user=json.dumps({"appkey": self.appkey})
            )
            
            content = response.choices[0].message.content
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                try:
                    enhanced_rec = json.loads(json_match.group())
                    # Preserve original ID and update metadata
                    enhanced_rec['id'] = original_rec.get('id', f"enhanced_{abs(hash(problem))}")
                    enhanced_rec['last_updated'] = datetime.now().strftime('%Y-%m-%d')
                    return enhanced_rec
                except json.JSONDecodeError:
                    logger.warning("Failed to parse LLM JSON response for enhancement")
            
        except Exception as e:
            logger.error(f"LLM enhancement failed: {e}")
        
        return None

    def _retrieve_historical_context(self):
        """Fetch historical data to provide context for LLM analysis."""
        historical_data_path = "data/processed/historical_logs.json"
        if os.path.exists(historical_data_path):
            with open(historical_data_path, "r") as file:
                return json.load(file)
        return []

    def process_query_with_context(self, query):
        """Process a query with historical context for enhanced recommendations."""
        historical_context = self._retrieve_historical_context()
        context_summary = "\n".join([
            f"Error: {entry['error']}, Frequency: {entry['frequency']}, Resolution: {entry['resolution']}"
            for entry in historical_context
        ])

        full_prompt = f"Historical Context:\n{context_summary}\n\nQuery:\n{query}"
        return self.process_query(full_prompt)
    
# Example usage and testing
if __name__ == "__main__":
    # Test the processor
    processor = NaturalLanguageQueryProcessor()
    
    test_queries = [
        "Show me all errors from the authentication service in the last hour",
        "Find critical failures in the payment system from yesterday",
        "What happened with user logins in the last 24 hours?",
        "Show me all database connection timeouts this week",
        "Find all exceptions in the API gateway",
        "What performance issues occurred in the last 30 minutes?"
    ]
    
    print("🔍 Natural Language Query Processor Test")
    print("=" * 60)
    
    for query in test_queries:
        print(f"\nQuery: {query}")
        result = processor.process_query(query)
        print(f"Type: {result['query_type']}")
        print(f"Confidence: {result['confidence']:.2f}")
        print(f"Keywords: {result['keywords']}")
        print(f"Filters: {result['search_filters']}")
        if result['time_range']:
            start, end = result['time_range']
            print(f"Time Range: {start.strftime('%Y-%m-%d %H:%M')} to {end.strftime('%Y-%m-%d %H:%M')}")
        print("-" * 40)
    
    # Test suggestions
    print("\n🎯 Query Suggestions:")
    suggestions = processor.get_query_suggestions()
    for i, suggestion in enumerate(suggestions, 1):
        print(f"{i}. {suggestion}")
