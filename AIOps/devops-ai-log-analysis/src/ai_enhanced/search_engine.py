"""
Natural Language Search Engine for DevOps AI Log Analysis
Executes structured queries generated from natural language input.
"""

import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NaturalLanguageSearchEngine:
    """
    Execute natural language queries against log data.
    Integrates with existing anomaly detection and analysis modules.
    """
    
    def __init__(self):
        """Initialize the search engine with connections to analysis modules."""
        self.search_history = []
        self.cached_results = {}
        
    def execute_search(self, search_params: Dict[str, Any], log_data: List[Dict] = None) -> Dict[str, Any]:
        """
        Execute a search based on structured parameters from natural language processing.
        
        Args:
            search_params: Structured search parameters from NaturalLanguageQueryProcessor
            log_data: Optional log data to search (if not provided, will load from files)
            
        Returns:
            Dict containing search results and metadata
        """
        logger.info(f"Executing search: {search_params['original_query']}")
        
        # Load log data if not provided
        if log_data is None:
            log_data = self._load_log_data()
        
        # Apply time filtering
        filtered_logs = self._apply_time_filter(log_data, search_params.get('time_range'))
        
        # Apply service filtering
        filtered_logs = self._apply_service_filter(filtered_logs, search_params.get('service'))
        
        # Apply severity filtering
        filtered_logs = self._apply_severity_filter(filtered_logs, search_params.get('severity'))
        
        # Apply keyword filtering
        filtered_logs = self._apply_keyword_filter(filtered_logs, search_params.get('keywords', []))
        
        # Apply error type filtering
        filtered_logs = self._apply_error_type_filter(filtered_logs, search_params.get('error_types', []))
        
        # Apply status filtering
        filtered_logs = self._apply_status_filter(filtered_logs, search_params.get('status'))
        
        # Apply count limit
        count_limit = search_params.get('count_limit', 50)
        if len(filtered_logs) > count_limit:
            filtered_logs = filtered_logs[:count_limit]
        
        # Analyze results
        analysis = self._analyze_results(filtered_logs, search_params)
        
        # Generate insights
        insights = self._generate_insights(filtered_logs, search_params)
        
        # Create response
        response = {
            'query': search_params['original_query'],
            'query_type': search_params.get('query_type', 'general_search'),
            'confidence': search_params.get('confidence', 0.0),
            'processing_method': search_params.get('processing_method', 'pattern'),
            'total_results': len(filtered_logs),
            'results': filtered_logs,
            'analysis': analysis,
            'insights': insights,
            'timestamp': datetime.now().isoformat(),
            'execution_time_ms': self._calculate_execution_time(),
            'filters_applied': self._get_applied_filters(search_params)
        }
        
        # Cache results
        self._cache_results(search_params['original_query'], response)
        
        # Add to search history
        self.search_history.append({
            'query': search_params['original_query'],
            'timestamp': datetime.now().isoformat(),
            'result_count': len(filtered_logs),
            'query_type': search_params.get('query_type')
        })
        
        logger.info(f"Search completed: {len(filtered_logs)} results found")
        return response
    
    def _load_log_data(self) -> List[Dict]:
        """Load log data from available sources."""
        log_data = []
        
        try:
            # Try to load from various sources
            from ingestion.ingest_text import ingest_text_logs
            from ingestion.ingest_json import ingest_json_logs
            from ingestion.ingest_xml import ingest_xml_logs
            
            # Load text logs
            try:
                text_logs = ingest_text_logs('data/raw/logs.txt')
                log_data.extend(text_logs)
            except:
                pass
            
            try:
                multiline_logs = ingest_text_logs('data/raw/test_stack_trace_logs.txt', detect_multiline=True)
                log_data.extend(multiline_logs)
            except:
                pass
            
            # Load JSON logs
            try:
                json_logs = ingest_json_logs('data/raw/logs.json')
                log_data.extend(json_logs)
            except:
                pass
            
            # Load XML logs
            try:
                xml_logs = ingest_xml_logs('data/raw/logs.xml')
                log_data.extend(xml_logs)
            except:
                pass
            
        except Exception as e:
            logger.warning(f"Error loading log data: {e}")
            # Return sample data for demonstration
            log_data = self._get_sample_log_data()
        
        return log_data
    
    def _apply_time_filter(self, logs: List[Dict], time_range: Optional[tuple]) -> List[Dict]:
        """Apply time-based filtering."""
        if not time_range:
            return logs
        
        start_time, end_time = time_range
        filtered = []
        
        for log in logs:
            # Handle both dict and string log entries
            if isinstance(log, dict):
                log_time = self._parse_log_timestamp(log.get('timestamp'))
            else:
                # For string entries, try to extract timestamp from the beginning
                log_time = self._parse_log_timestamp(str(log)[:25])  # First 25 chars usually contain timestamp
            
            if log_time and start_time <= log_time <= end_time:
                filtered.append(log)
            elif not time_range:  # If no time filter, include all logs
                filtered.append(log)
        
        return filtered
    
    def _apply_service_filter(self, logs: List[Dict], service: Optional[str]) -> List[Dict]:
        """Apply service-based filtering."""
        if not service:
            return logs
        
        filtered = []
        for log in logs:
            # Handle both dict and string log entries
            if isinstance(log, dict):
                log_service = log.get('service', '').lower()
            else:
                # For string entries, try to extract service from the string
                log_service = str(log).lower()
            
            if service.lower() in log_service or log_service in service.lower():
                filtered.append(log)
        
        return filtered
    
    def _apply_severity_filter(self, logs: List[Dict], severity: Optional[str]) -> List[Dict]:
        """Apply severity-based filtering."""
        if not severity:
            return logs
        
        filtered = []
        for log in logs:
            # Handle both dict and string log entries
            if isinstance(log, dict):
                log_severity = log.get('level', '').upper()
            else:
                # For string entries, try to extract severity from the string
                log_severity = ''
                if isinstance(log, str):
                    log_upper = log.upper()
                    if 'ERROR' in log_upper:
                        log_severity = 'ERROR'
                    elif 'WARNING' in log_upper or 'WARN' in log_upper:
                        log_severity = 'WARNING'
                    elif 'INFO' in log_upper:
                        log_severity = 'INFO'
                    elif 'DEBUG' in log_upper:
                        log_severity = 'DEBUG'
                    elif 'CRITICAL' in log_upper:
                        log_severity = 'CRITICAL'
            
            if severity.upper() in log_severity:
                filtered.append(log)
        
        return filtered
    
    def _apply_keyword_filter(self, logs: List[Dict], keywords: List[str]) -> List[Dict]:
        """Apply keyword-based filtering."""
        if not keywords:
            return logs
        
        filtered = []
        for log in logs:
            # Handle both dict and string log entries
            if isinstance(log, dict):
                log_text = (log.get('message', '') + ' ' + log.get('raw_log', '')).lower()
            else:
                log_text = str(log).lower()
            
            if any(keyword.lower() in log_text for keyword in keywords):
                filtered.append(log)
        
        return filtered
    
    def _apply_error_type_filter(self, logs: List[Dict], error_types: List[str]) -> List[Dict]:
        """Apply error type filtering."""
        if not error_types:
            return logs
        
        filtered = []
        for log in logs:
            # Handle both dict and string log entries
            if isinstance(log, dict):
                log_text = (log.get('message', '') + ' ' + log.get('raw_log', '')).lower()
            else:
                log_text = str(log).lower()
            
            if any(error_type.lower() in log_text for error_type in error_types):
                filtered.append(log)
        
        return filtered
    
    def _apply_status_filter(self, logs: List[Dict], status: Optional[str]) -> List[Dict]:
        """Apply status-based filtering."""
        if not status:
            return logs
        
        filtered = []
        for log in logs:
            # Handle both dict and string log entries
            if isinstance(log, dict):
                log_text = (log.get('message', '') + ' ' + log.get('raw_log', '')).lower()
            else:
                log_text = str(log).lower()
            
            if status.lower() in log_text:
                filtered.append(log)
        
        return filtered
    
    def _parse_log_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """Parse log timestamp string to datetime object."""
        if not timestamp_str:
            return None
        
        # Common timestamp formats
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%S.%fZ',
            '%d/%m/%Y %H:%M:%S',
            '%m/%d/%Y %H:%M:%S'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(timestamp_str, fmt)
            except ValueError:
                continue
        
        return None
    
    def _analyze_results(self, results: List[Dict], search_params: Dict) -> Dict[str, Any]:
        """Analyze search results to provide insights."""
        analysis = {
            'total_count': len(results),
            'severity_distribution': {},
            'service_distribution': {},
            'time_distribution': {},
            'common_patterns': [],
            'anomalies_found': 0
        }
        
        # Analyze severity distribution
        for log in results:
            if isinstance(log, dict):
                severity = log.get('level', 'UNKNOWN')
            else:
                # Extract severity from string
                severity = 'UNKNOWN'
                log_str = str(log).upper()
                if 'ERROR' in log_str:
                    severity = 'ERROR'
                elif 'WARNING' in log_str or 'WARN' in log_str:
                    severity = 'WARNING'
                elif 'INFO' in log_str:
                    severity = 'INFO'
                elif 'DEBUG' in log_str:
                    severity = 'DEBUG'
                elif 'CRITICAL' in log_str:
                    severity = 'CRITICAL'
            
            analysis['severity_distribution'][severity] = analysis['severity_distribution'].get(severity, 0) + 1
        
        # Analyze service distribution
        for log in results:
            if isinstance(log, dict):
                service = log.get('service', 'unknown')
            else:
                service = 'unknown'
            
            analysis['service_distribution'][service] = analysis['service_distribution'].get(service, 0) + 1
        
        # Look for anomalies (stack traces, repeated errors, etc.)
        anomaly_count = 0
        for log in results:
            if isinstance(log, dict):
                log_text = log.get('message', '') + ' ' + log.get('raw_log', '')
            else:
                log_text = str(log)
            
            if 'traceback' in log_text.lower() or 'exception' in log_text.lower():
                anomaly_count += 1
        
        analysis['anomalies_found'] = anomaly_count
        
        return analysis
    
    def _generate_insights(self, results: List[Dict], search_params: Dict) -> List[str]:
        """Generate insights based on search results."""
        insights = []
        
        if not results:
            insights.append("No logs found matching your criteria. Try broadening your search terms or time range.")
            return insights
        
        # Time-based insights
        time_range = search_params.get('time_range')
        if time_range:
            start, end = time_range
            duration = end - start
            insights.append(f"Found {len(results)} log entries in the last {self._format_duration(duration)}.")
        
        # Severity insights
        error_count = 0
        for log in results:
            if isinstance(log, dict):
                level = log.get('level', '').upper()
            else:
                level = str(log).upper()
            
            if any(err_level in level for err_level in ['ERROR', 'CRITICAL', 'FATAL']):
                error_count += 1
        
        if error_count > 0:
            insights.append(f"⚠️ {error_count} error-level entries found - may require attention.")
        
        # Service insights
        services = set()
        for log in results:
            if isinstance(log, dict):
                service = log.get('service', 'unknown')
            else:
                service = 'unknown'
            services.add(service)
        
        if len(services) > 1:
            insights.append(f"Logs span across {len(services)} services: {', '.join(sorted(services))}")
        
        # Pattern insights
        stack_traces = 0
        for log in results:
            if isinstance(log, dict):
                message = log.get('message', '').lower()
            else:
                message = str(log).lower()
            
            if 'traceback' in message:
                stack_traces += 1
        
        if stack_traces > 0:
            insights.append(f"🔍 {stack_traces} stack traces detected - potential application issues.")
        
        # Query type specific insights
        query_type = search_params.get('query_type', '')
        if query_type == 'security_audit':
            insights.append("🔒 Security audit results - review for potential threats or unauthorized access.")
        elif query_type == 'performance_monitoring':
            insights.append("⚡ Performance monitoring results - look for patterns in slow operations.")
        elif query_type == 'error_analysis':
            insights.append("🐛 Error analysis results - consider root cause analysis for recurring issues.")
        
        return insights
    
    def _format_duration(self, duration) -> str:
        """Format timedelta duration to human-readable string."""
        total_seconds = int(duration.total_seconds())
        days = total_seconds // 86400
        hours = (total_seconds % 86400) // 3600
        minutes = (total_seconds % 3600) // 60
        
        if days > 0:
            return f"{days} day{'s' if days > 1 else ''}"
        elif hours > 0:
            return f"{hours} hour{'s' if hours > 1 else ''}"
        elif minutes > 0:
            return f"{minutes} minute{'s' if minutes > 1 else ''}"
        else:
            return f"{total_seconds} second{'s' if total_seconds > 1 else ''}"
    
    def _calculate_execution_time(self) -> int:
        """Calculate execution time in milliseconds."""
        # This is a placeholder - in real implementation, you'd track actual execution time
        return 150  # ms
    
    def _get_applied_filters(self, search_params: Dict) -> Dict[str, Any]:
        """Get summary of applied filters."""
        filters = {}
        
        if search_params.get('time_range'):
            start, end = search_params['time_range']
            filters['time_range'] = f"{start.strftime('%Y-%m-%d %H:%M')} to {end.strftime('%Y-%m-%d %H:%M')}"
        
        if search_params.get('service'):
            filters['service'] = search_params['service']
        
        if search_params.get('severity'):
            filters['severity'] = search_params['severity']
        
        if search_params.get('keywords'):
            filters['keywords'] = search_params['keywords']
        
        if search_params.get('error_types'):
            filters['error_types'] = search_params['error_types']
        
        return filters
    
    def _cache_results(self, query: str, results: Dict):
        """Cache search results for quick retrieval."""
        cache_key = query.lower().strip()
        self.cached_results[cache_key] = {
            'results': results,
            'timestamp': datetime.now(),
            'hit_count': 1
        }
        
        # Keep cache size reasonable
        if len(self.cached_results) > 100:
            # Remove oldest entries
            oldest_key = min(self.cached_results.keys(), 
                           key=lambda k: self.cached_results[k]['timestamp'])
            del self.cached_results[oldest_key]
    
    def get_cached_results(self, query: str) -> Optional[Dict]:
        """Get cached results for a query."""
        cache_key = query.lower().strip()
        if cache_key in self.cached_results:
            cached = self.cached_results[cache_key]
            # Check if cache is still fresh (within 5 minutes)
            if (datetime.now() - cached['timestamp']).seconds < 300:
                cached['hit_count'] += 1
                return cached['results']
        return None
    
    def get_search_history(self, limit: int = 10) -> List[Dict]:
        """Get recent search history."""
        return self.search_history[-limit:]
    
    def _get_sample_log_data(self) -> List[Dict]:
        """Get sample log data for demonstration."""
        return [
            {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'level': 'ERROR',
                'service': 'auth-service',
                'message': 'Authentication failed <NAME_EMAIL>',
                'raw_log': '2024-01-15 10:30:48 ERROR Authentication failed <NAME_EMAIL>'
            },
            {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'level': 'INFO',
                'service': 'user-service',
                'message': 'User login successful',
                'raw_log': '2024-01-15 10:30:45 INFO User login successful'
            },
            {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'level': 'CRITICAL',
                'service': 'payment-service',
                'message': 'Database connection timeout',
                'raw_log': '2024-01-15 10:31:00 CRITICAL Database connection timeout'
            }
        ]

# Example usage
if __name__ == "__main__":
    # Test the search engine
    search_engine = NaturalLanguageSearchEngine()
    
    # Sample search parameters (would come from NaturalLanguageQueryProcessor)
    test_params = {
        'original_query': 'Show me all authentication errors in the last hour',
        'query_type': 'security_audit',
        'confidence': 0.85,
        'keywords': ['authentication', 'error'],
        'error_types': ['auth', 'error'],
        'time_range': (datetime.now() - timedelta(hours=1), datetime.now()),
        'count_limit': 50
    }
    
    print("🔍 Natural Language Search Engine Test")
    print("=" * 60)
    
    results = search_engine.execute_search(test_params)
    
    print(f"Query: {results['query']}")
    print(f"Results: {results['total_results']}")
    print(f"Confidence: {results['confidence']:.2f}")
    print(f"Processing Method: {results['processing_method']}")
    print(f"Execution Time: {results['execution_time_ms']}ms")
    print("\nInsights:")
    for insight in results['insights']:
        print(f"  • {insight}")
    
    print("\nAnalysis:")
    print(f"  • Severity Distribution: {results['analysis']['severity_distribution']}")
    print(f"  • Service Distribution: {results['analysis']['service_distribution']}")
    print(f"  • Anomalies Found: {results['analysis']['anomalies_found']}")
