def load_data(file_path, file_type):
    if file_type == 'text':
        with open(file_path, 'r') as file:
            return file.readlines()
    elif file_type == 'json':
        import json
        with open(file_path, 'r') as file:
            return json.load(file)
    elif file_type == 'xml':
        import xml.etree.ElementTree as ET
        tree = ET.parse(file_path)
        return tree.getroot()
    elif file_type == 'timeseries':
        import pandas as pd
        return pd.read_csv(file_path, parse_dates=True, index_col=0)
    else:
        raise ValueError("Unsupported file type: {}".format(file_type))

def save_data(data, file_path, file_type):
    if file_type == 'text':
        with open(file_path, 'w') as file:
            file.writelines(data)
    elif file_type == 'json':
        import json
        with open(file_path, 'w') as file:
            json.dump(data, file, indent=4)
    elif file_type == 'xml':
        import xml.etree.ElementTree as ET
        tree = ET.ElementTree(data)
        tree.write(file_path)
    elif file_type == 'timeseries':
        import pandas as pd
        data.to_csv(file_path)
    else:
        raise ValueError("Unsupported file type: {}".format(file_type))

def transform_data(data, transformation_type):
    if transformation_type == 'normalize':
        return [(x - min(data)) / (max(data) - min(data)) for x in data]
    elif transformation_type == 'standardize':
        mean = sum(data) / len(data)
        std_dev = (sum((x - mean) ** 2 for x in data) / len(data)) ** 0.5
        return [(x - mean) / std_dev for x in data]
    else:
        raise ValueError("Unsupported transformation type: {}".format(transformation_type))