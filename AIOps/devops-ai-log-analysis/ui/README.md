# DevOps AI Log Analysis - Web UI

A modern, AI-powered web interface for log analysis and root cause detection. This web application provides an intuitive interface for uploading log files, viewing analysis results, and interacting with advanced root cause analysis features.

## Features

### 🎯 Core Functionality
- **File Upload**: Support for multiple log formats (TXT, JSON, XML, CSV, LOG)
- **Real-time Processing**: Advanced AI-powered log analysis pipeline
- **Interactive Dashboard**: Modern, responsive web interface
- **Visual Analytics**: Charts and graphs for data visualization
- **Root Cause Analysis**: AI-powered identification of underlying issues

### 📊 Analysis Capabilities
- **Error Classification**: Automatic categorization of errors by type and severity
- **Anomaly Detection**: Machine learning-based anomaly identification
- **Pattern Recognition**: Advanced pattern matching for root cause identification
- **Trend Analysis**: Historical trend visualization and insights
- **Priority Scoring**: Intelligent priority assignment for issues

### 🎨 User Interface
- **Modern Design**: Clean, responsive interface built with Bootstrap 5
- **Interactive Charts**: Dynamic visualizations using Chart.js
- **Tabbed Interface**: Organized display of different analysis results
- **Real-time Updates**: Live progress tracking during file processing
- **Mobile Responsive**: Works on desktop, tablet, and mobile devices

## Quick Start

### Prerequisites
- Python 3.8 or higher
- Virtual environment (recommended)
- 4GB+ RAM for optimal performance

### Installation & Setup

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd devops-ai-log-analysis
   ```

2. **Create and activate virtual environment**:
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Start the web UI**:
   ```bash
   python start_ui.py
   ```

   The startup script will automatically:
   - Install required dependencies
   - Create necessary directories
   - Start the Flask web server

4. **Access the application**:
   - Open your browser and go to: `http://localhost:5000`
   - The web interface will be available immediately

### Alternative Manual Setup

If you prefer to set up manually:

```bash
# Install dependencies
pip install -r requirements.txt

# Create directories
mkdir -p ui/uploads ui/static data/processed data/raw models

# Start the server
cd ui
python app.py
```

## Usage Guide

### 1. Upload Log Files

1. Navigate to the **Upload** page
2. Drag and drop your log file or click to browse
3. Supported formats: `.txt`, `.log`, `.json`, `.xml`, `.csv`
4. Maximum file size: 16MB
5. Click "Upload & Analyze" to process the file

### 2. View Analysis Results

After processing, you'll see comprehensive analysis results in multiple tabs:

#### **Root Cause Analysis Tab**
- **Summary**: Overview of identified issues and patterns
- **Recommendations**: Actionable insights with priority levels
- **Frequency Analysis**: Most common root causes
- **Priority Scores**: Risk assessment for each issue type

#### **Error Classification Tab**
- **Categorized Errors**: Errors grouped by type and severity
- **Error Details**: Specific error messages and context
- **Count Statistics**: Number of errors per category

#### **Anomaly Detection Tab**
- **Detected Anomalies**: Unusual patterns and outliers
- **Severity Levels**: Critical, high, medium, low classifications
- **Timestamps**: When anomalies were detected

#### **Visual Analysis Tab**
- **Error Distribution**: Pie charts showing error categories
- **Root Cause Frequency**: Bar charts of common causes
- **Priority Radar**: Radar chart of priority scores
- **Severity Breakdown**: Visual severity distribution

### 3. Demo Mode

Try the demo mode to explore features without uploading files:
1. Click "Demo" in the navigation
2. View sample analysis results
3. Explore all interface features

## API Endpoints

The web UI also provides REST API endpoints for integration:

- `GET /` - Main dashboard
- `POST /upload` - Upload and process log files
- `GET /analysis` - View analysis results
- `GET /api/analysis/<id>` - Get specific analysis results (JSON)
- `GET /api/demo` - Load demo data
- `GET /demo` - Demo page with sample data

## Configuration

### Environment Variables

You can configure the application using environment variables:

```bash
export FLASK_ENV=development  # or production
export FLASK_DEBUG=True       # for development
export MAX_CONTENT_LENGTH=16777216  # 16MB file size limit
```

### File Upload Settings

In `ui/app.py`, you can modify:
- `MAX_CONTENT_LENGTH`: Maximum file size (default: 16MB)
- `ALLOWED_EXTENSIONS`: Supported file types
- `UPLOAD_FOLDER`: Directory for temporary file storage

## Architecture

### Backend Components
- **Flask Web Framework**: Main web server
- **File Processing Pipeline**: Integration with existing analysis modules
- **Session Management**: Secure storage of analysis results
- **API Layer**: REST endpoints for data access

### Frontend Components
- **Bootstrap 5**: Modern, responsive UI framework
- **Chart.js**: Interactive data visualization
- **Font Awesome**: Icon library
- **Custom CSS**: Enhanced styling and themes

### Data Flow
1. User uploads log file via web interface
2. File is validated and temporarily stored
3. Analysis pipeline processes the file:
   - Ingestion → Preprocessing → Classification → Anomaly Detection → Root Cause Analysis
4. Results are stored in session and displayed in UI
5. Interactive charts and tables present the analysis

## Security Features

- **File Type Validation**: Only allowed file types are processed
- **File Size Limits**: Prevents oversized uploads
- **Secure Filenames**: Sanitized file names prevent directory traversal
- **Session Security**: Secure session management
- **Input Validation**: All user inputs are validated
- **Temporary File Cleanup**: Uploaded files are automatically cleaned up

## Performance Optimization

- **Streaming Processing**: Large files are processed in chunks
- **Caching**: Analysis results are cached for quick access
- **Async Processing**: Non-blocking file processing
- **Memory Management**: Efficient memory usage for large files
- **Database-free**: No database dependencies for simplicity

## Troubleshooting

### Common Issues

1. **Port Already in Use**:
   ```bash
   # Kill process using port 5000
   lsof -ti:5000 | xargs kill -9
   ```

2. **Module Import Errors**:
   ```bash
   # Reinstall dependencies
   pip install -r requirements.txt
   ```

3. **Permission Errors**:
   ```bash
   # Check file permissions
   chmod +x start_ui.py
   ```

4. **Memory Issues**:
   - Reduce file size or split large files
   - Increase system memory if possible
   - Use streaming processing for very large files

### Debug Mode

For development, enable debug mode:
```bash
export FLASK_DEBUG=True
python ui/app.py
```

## Development

### Adding New Features

1. **Backend**: Modify `ui/app.py` to add new routes or API endpoints
2. **Frontend**: Update HTML templates in `ui/templates/`
3. **Styling**: Add custom CSS in the `<style>` sections
4. **Charts**: Use Chart.js for new visualizations

### Testing

Run the application in development mode:
```bash
cd ui
python app.py
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the troubleshooting section above
- Review the existing documentation
- Open an issue on the project repository

---

**Built with ❤️ for DevOps professionals**
