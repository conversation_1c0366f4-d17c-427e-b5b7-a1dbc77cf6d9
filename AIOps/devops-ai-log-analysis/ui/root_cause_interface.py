#!/usr/bin/env python3
"""
Root Cause Analysis Web Interface
Provides a web-based interface for intelligent root cause analysis with AI-powered recommendations.
"""

import os
import sys
import json
from datetime import datetime
from flask import Blueprint, render_template, request, jsonify, session

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import our new AI-enhanced modules
from ai_enhanced.intelligent_root_cause import IntelligentRootCauseAnalyzer, root_cause_analyzer
from ai_enhanced.knowledge_base import knowledge_base

# Create blueprint
root_cause_bp = Blueprint('root_cause', __name__)

@root_cause_bp.route('/root-cause')
def root_cause_page():
    """Main root cause analysis page"""
    return render_template('root_cause_analysis.html')

@root_cause_bp.route('/api/root-cause/analyze', methods=['POST'])
def analyze_root_cause_api():
    """API endpoint for root cause analysis using knowledge base"""
    try:
        data = request.get_json()
        
        if not data or 'error_message' not in data:
            return jsonify({'error': 'Error message is required'}), 400
        
        error_message = data['error_message']
        context = data.get('context', {})
        
        # Perform root cause analysis using our intelligent analyzer
        result = root_cause_analyzer.analyze_error(error_message, context)
        
        # Convert to dict for JSON response
        analysis_data = {
            'error_id': result.error_id,
            'timestamp': result.timestamp,
            'error_message': result.error_message,
            'category': result.category,
            'severity': result.severity,
            'confidence_score': result.confidence_score,
            'recommendations': result.recommendations,
            'related_patterns': result.related_patterns,
            'suggested_actions': result.suggested_actions,
            'prevention_tips': result.prevention_tips,
            'diagnostic_commands': result.diagnostic_commands,
            'estimated_resolution_time': result.estimated_resolution_time
        }
        
        return jsonify({
            'success': True,
            'analysis': analysis_data
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@root_cause_bp.route('/api/root-cause/analyze-multiple', methods=['POST'])
def analyze_multiple_errors():
    """Analyze multiple error messages."""
    try:
        data = request.get_json()
        
        if not data or 'error_messages' not in data:
            return jsonify({'error': 'Error messages are required'}), 400
        
        error_messages = data['error_messages']
        
        if not isinstance(error_messages, list):
            return jsonify({'error': 'Error messages must be a list'}), 400
        
        # Perform analysis
        result = root_cause_analyzer.analyze_multiple_errors(error_messages)
        
        return jsonify({
            'success': True,
            'analysis': result
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@root_cause_bp.route('/api/root-cause/suggestions', methods=['GET'])
def get_error_suggestions():
    """Get sample error messages for testing."""
    suggestions = [
        "MySQL connection timeout after 30 seconds",
        "Jenkins build failed: compilation error in UserService.java",
        "Docker container exited with code 1: port 8080 already in use",
        "Kubernetes pod in CrashLoopBackOff state",
        "Database deadlock detected in transaction",
        "SSL certificate expired for api.example.com",
        "Out of memory error: Java heap space",
        "Disk full error: No space left on device",
        "Network connection refused on port 443",
        "SonarQube quality gate failed: code coverage below 80%",
        "Authentication failed: invalid credentials",
        "High CPU usage: load average above 5.0",
        "DNS resolution failed for database.internal",
        "Maven build failure: dependency not found",
        "Application startup failed: configuration file not found"
    ]
    
    return jsonify({
        'success': True,
        'suggestions': suggestions
    })

@root_cause_bp.route('/api/root-cause/knowledge-base/search', methods=['GET'])
def search_knowledge_base():
    """Search the knowledge base."""
    try:
        query = request.args.get('query', '')
        category = request.args.get('category', '')
        severity = request.args.get('severity', '')
        
        results = []
        
        if query:
            # Search by keywords
            keywords = query.split()
            results = knowledge_base.search_by_keywords(keywords)
        elif category:
            # Search by category
            results = knowledge_base.search_by_category(category)
        elif severity:
            # Search by severity
            results = knowledge_base.search_by_severity(severity)
        
        # Convert results to dict format
        search_results = []
        for entry in results:
            search_results.append({
                'id': entry.id,
                'category': entry.category,
                'problem': entry.problem,
                'symptoms': entry.symptoms,
                'solutions': entry.solutions,
                'severity': entry.severity,
                'tools': entry.tools
            })
        
        return jsonify({
            'success': True,
            'results': search_results,
            'total': len(search_results)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@root_cause_bp.route('/api/root-cause/knowledge-base/categories', methods=['GET'])
def get_categories():
    """Get all available categories."""
    try:
        categories = knowledge_base.get_categories()
        return jsonify({
            'success': True,
            'categories': categories
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@root_cause_bp.route('/api/root-cause/statistics', methods=['GET'])
def get_statistics():
    """Get root cause analysis statistics."""
    try:
        stats = root_cause_analyzer.get_analysis_stats()
        
        return jsonify({
            'success': True,
            'statistics': stats
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@root_cause_bp.route('/api/root-cause/upload', methods=['POST'])
def upload_logs_for_analysis():
    """Upload log files for root cause analysis"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400
        
        # Read file content
        content = file.read().decode('utf-8')
        
        # Split into log entries
        log_entries = []
        for line in content.strip().split('\n'):
            if line.strip():
                log_entries.append(line.strip())
        
        # Store in session for analysis
        session['uploaded_logs'] = log_entries
        
        return jsonify({
            'success': True,
            'message': f'Successfully uploaded {len(log_entries)} log entries',
            'log_count': len(log_entries)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@root_cause_bp.route('/api/root-cause/analyze-uploaded', methods=['POST'])
def analyze_uploaded_logs():
    """Analyze uploaded log files"""
    try:
        # Get uploaded logs from session
        log_entries = session.get('uploaded_logs', [])
        
        if not log_entries:
            return jsonify({
                'success': False,
                'error': 'No logs uploaded. Please upload a log file first.'
            }), 400
        
        # Get analysis options
        data = request.get_json() or {}
        include_anomalies = data.get('include_anomalies', True)
        focus_critical = data.get('focus_critical', False)
        
        # Filter for critical errors if requested
        if focus_critical:
            critical_keywords = ['error', 'exception', 'failed', 'timeout', 'denied']
            filtered_logs = []
            for log in log_entries:
                if any(keyword in log.lower() for keyword in critical_keywords):
                    filtered_logs.append(log)
            log_entries = filtered_logs
        
        # Step 1: Classify errors
        classified_errors = classify_errors(log_entries)
        
        # Step 2: Detect anomalies (if requested)
        anomaly_data = None
        if include_anomalies:
            try:
                anomaly_data = detect_anomalies(log_entries)
            except Exception as e:
                # Continue without anomaly detection if it fails
                print(f"Anomaly detection failed: {e}")
        
        # Step 3: Perform root cause analysis
        analysis_results = analyze_root_cause_with_ai(classified_errors, anomaly_data)
        
        # Step 4: Format response
        response = {
            'success': True,
            'analysis': analysis_results,
            'input_summary': {
                'total_logs': len(log_entries),
                'classified_errors': classified_errors.get('total_errors', 0),
                'anomalies_detected': len(anomaly_data.get('anomalies', [])) if anomaly_data else 0,
                'analysis_timestamp': datetime.now().isoformat(),
                'options_used': {
                    'include_anomalies': include_anomalies,
                    'focus_critical': focus_critical
                }
            }
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@root_cause_bp.route('/api/root-cause/export', methods=['POST'])
def export_analysis_results():
    """Export analysis results to JSON or CSV"""
    try:
        data = request.get_json()
        analysis_results = data.get('analysis_results')
        format_type = data.get('format', 'json')
        
        if not analysis_results:
            return jsonify({
                'success': False,
                'error': 'No analysis results provided'
            }), 400
        
        # Generate filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'root_cause_analysis_{timestamp}.{format_type}'
        
        if format_type == 'json':
            # Export as JSON
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'format': 'json',
                    'version': '1.0'
                },
                'analysis_results': analysis_results
            }
            
            return jsonify({
                'success': True,
                'filename': filename,
                'data': export_data
            })
        
        else:
            return jsonify({
                'success': False,
                'error': f'Unsupported format: {format_type}'
            }), 400
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
