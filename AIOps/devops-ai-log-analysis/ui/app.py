#!/usr/bin/env python3
"""
DevOps AI Log Analysis Flask Application
Main web interface for log analysis with AI-powered insights
"""

import os
import sys
import json
import time
import traceback
from datetime import datetime
from flask import Flask, request, jsonify, render_template, redirect, url_for, session, flash, send_file

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import analysis modules
try:
    from src.ingestion.ingest_text import ingest_text_logs
    from src.ingestion.ingest_json import ingest_json_logs
    from src.ingestion.ingest_xml import ingest_xml_logs
    from src.preprocessing.preprocess_text import preprocess_text
    from src.preprocessing.preprocess_json import preprocess_json
    from src.preprocessing.preprocess_xml import preprocess_xml
    from src.error_classification.classify_errors import classify_errors
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure you're running from the correct directory")
    sys.exit(1)

# Import chat interface
try:
    from .chat_interface import chat_bp
    from .root_cause_interface import root_cause_bp
except ImportError:
    # Fallback for direct execution
    try:
        from chat_interface import chat_bp
        from root_cause_interface import root_cause_bp
    except ImportError as e:
        print(f"Warning: Could not import chat interfaces: {e}")
        chat_bp = None
        root_cause_bp = None

app = Flask(__name__)
app.secret_key = 'dev-ops-ai-log-analysis-secret-key'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['PERMANENT_SESSION_LIFETIME'] = 3600  # 1 hour

# File-based demo storage to survive server restarts
DEMO_RESULTS_FILE = 'demo_results.json'

def save_demo_result(result):
    """Save demo result to file for persistence"""
    try:
        with open(DEMO_RESULTS_FILE, 'w') as f:
            json.dump(result, f, indent=2)
        print(f"💾 Saved demo result to {DEMO_RESULTS_FILE}")
    except Exception as e:
        print(f"❌ Failed to save demo result: {e}")

def load_demo_result():
    """Load demo result from file"""
    try:
        if os.path.exists(DEMO_RESULTS_FILE):
            with open(DEMO_RESULTS_FILE, 'r') as f:
                result = json.load(f)
            print(f"📂 Loaded demo result from {DEMO_RESULTS_FILE}")
            return result
    except Exception as e:
        print(f"❌ Failed to load demo result: {e}")
    return None

# Performance optimization: Lazy loading of heavy modules
def get_root_cause_analyzer():
    """Lazy load root cause analyzer to improve startup time"""
    global root_cause_analyzer
    if 'root_cause_analyzer' not in globals():
        from src.root_cause_analysis.unified_root_cause_analyzer import UnifiedRootCauseAnalyzer
        root_cause_analyzer = UnifiedRootCauseAnalyzer()
    return root_cause_analyzer

def get_anomaly_detector():
    """Lazy load anomaly detector to improve startup time"""
    global anomaly_detector
    if 'anomaly_detector' not in globals():
        from src.anomaly_detection.detect_anomalies import AdvancedAnomalyDetector
        anomaly_detector = AdvancedAnomalyDetector()
    return anomaly_detector

# Initialize analyzers
anomaly_detector = get_anomaly_detector()
root_cause_analyzer = get_root_cause_analyzer()

# In-memory storage for analysis results (use Redis or database in production)
analysis_storage = {}

# Register blueprints if available
if chat_bp:
    app.register_blueprint(chat_bp, url_prefix='/chat')
if root_cause_bp:
    app.register_blueprint(root_cause_bp, url_prefix='/root-cause')

def process_log_file(file_path, file_type):
    """Process uploaded log file through the complete pipeline"""
    try:
        # Step 1: Ingest data
        if file_type in ('text', 'log', 'csv'):
            # Read as text and use the complete analysis pipeline
            with open(file_path, 'r', encoding='utf-8') as f:
                log_text = f.read()
            
            # Step 2: Run root cause analysis
            analysis = root_cause_analyzer.analyze_log_text(log_text)
            
            # Step 3: Run error classification
            log_lines = [line for line in log_text.split('\n') if line.strip()]
            classified_errors = classify_errors(log_lines)
            
            # Step 4: Run anomaly detection
            anomalies = anomaly_detector.detect_anomalies([{'message': line} for line in log_lines])
            
            return {
                'success': True,
                'analysis': analysis,
                'classified_errors': classified_errors,
                'root_cause_analysis': analysis,
                'anomalies': anomalies,
                'file_info': {
                    'name': os.path.basename(file_path),
                    'size': os.path.getsize(file_path),
                    'type': file_type,
                    'processed_at': datetime.now().isoformat()
                }
            }
        elif file_type == 'json':
            # Process JSON logs
            ingested_data = ingest_json_logs(file_path)
            processed_data = preprocess_json(ingested_data)
            classified_errors = classify_errors(processed_data)
            anomalies = anomaly_detector.detect_anomalies(processed_data)
            
            # Convert processed data to text for unified analysis
            log_text = '\n'.join([str(item) for item in processed_data if isinstance(item, (str, dict))])
            root_causes = root_cause_analyzer.analyze_log_text(log_text)
            
            return {
                'success': True,
                'ingested_data': ingested_data,
                'processed_data': processed_data,
                'classified_errors': classified_errors,
                'anomalies': anomalies,
                'root_cause_analysis': root_causes,
                'analysis': root_causes,  # Add for template compatibility
                'file_info': {
                    'name': os.path.basename(file_path),
                    'size': os.path.getsize(file_path),
                    'type': file_type,
                    'processed_at': datetime.now().isoformat()
                }
            }
        elif file_type == 'xml':
            # Process XML logs
            ingested_data = ingest_xml_logs(file_path)
            processed_data = preprocess_xml(ingested_data)
            classified_errors = classify_errors(processed_data)
            anomalies = anomaly_detector.detect_anomalies(processed_data)
            
            # Convert processed data to text for unified analysis
            log_text = '\n'.join([str(item) for item in processed_data if isinstance(item, (str, dict))])
            root_causes = root_cause_analyzer.analyze_log_text(log_text)
            
            return {
                'success': True,
                'ingested_data': ingested_data,
                'processed_data': processed_data,
                'classified_errors': classified_errors,
                'anomalies': anomalies,
                'root_cause_analysis': root_causes,
                'analysis': root_causes,  # Add for template compatibility
                'file_info': {
                    'name': os.path.basename(file_path),
                    'size': os.path.getsize(file_path),
                    'type': file_type,
                    'processed_at': datetime.now().isoformat()
                }
            }
        else:
            # Default fallback: treat as text
            with open(file_path, 'r', encoding='utf-8') as f:
                log_text = f.read()
            analysis = root_cause_analyzer.analyze_log_text(log_text)
            
            # Add anomalies detection
            anomalies = anomaly_detector.detect_anomalies([{'message': line} for line in log_text.split('\n') if line.strip()])
            
            return {
                'success': True,
                'analysis': analysis,
                'classified_errors': analysis,  # Add this for template compatibility
                'root_cause_analysis': analysis,  # Add this for template compatibility
                'anomalies': anomalies,  # Add this for template compatibility
                'file_info': {
                    'name': os.path.basename(file_path),
                    'size': os.path.getsize(file_path),
                    'type': 'text',
                    'processed_at': datetime.now().isoformat()
                }
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }

@app.route('/')
def home():
    """Main dashboard"""
    return render_template('index.html')

@app.route('/upload', methods=['GET', 'POST'])
def upload():
    """File upload page"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected')
            return redirect(request.url)
        
        if file:
            # Save file
            filename = file.filename
            file_path = os.path.join('uploads', filename)
            os.makedirs('uploads', exist_ok=True)
            file.save(file_path)
            
            # Determine file type
            file_ext = filename.split('.')[-1].lower()
            file_type_map = {
                'txt': 'text', 'log': 'log', 'csv': 'csv',
                'json': 'json', 'xml': 'xml'
            }
            file_type = file_type_map.get(file_ext, 'text')
            
            # Process file
            result = process_log_file(file_path, file_type)
            
            if result['success']:
                # Store result in session
                session_id = f"analysis_{datetime.now().timestamp()}"
                analysis_storage[session_id] = result
                session['current_analysis'] = session_id
                
                flash('File uploaded and analyzed successfully!')
                return redirect(url_for('analysis_results'))
            else:
                flash(f'Error processing file: {result["error"]}')
                return redirect(request.url)
    
    return render_template('upload.html')

@app.route('/analysis')
def analysis():
    """Analysis results page"""
    session_id = session.get('current_analysis')
    if not session_id or session_id not in analysis_storage:
        flash('No analysis data found. Please upload a file first.')
        return redirect(url_for('upload'))
    
    result = analysis_storage[session_id]
    return render_template('analysis.html', result=result)

@app.route('/analysis_results')
def analysis_results():
    """Analysis results page for demo and session data"""
    # Check if we have session analysis result (from demo)
    if 'analysis_result' in session:
        result = session['analysis_result']
        print(f"📊 Found analysis result in session: {result.get('file_info', {}).get('name', 'unknown')}")
        print(f"[DEBUG] result keys: {list(result.keys())}")
        if 'analysis' in result:
            print(f"[DEBUG] analysis keys: {list(result['analysis'].keys())}")
            print(f"[DEBUG] extracted_errors count: {len(result['analysis'].get('extracted_errors', []))}")
        else:
            print("[DEBUG] No 'analysis' key in result!")
        return render_template('analysis.html', result=result)
    
    # Check if we have regular analysis session
    session_id = session.get('current_analysis')
    if session_id and session_id in analysis_storage:
        result = analysis_storage[session_id]
        print(f"📊 Found analysis result in storage: {result.get('file_info', {}).get('name', 'unknown')}")
        return render_template('analysis.html', result=result)
    
    # Check for demo result file (survives server restarts)
    demo_result = load_demo_result()
    if demo_result:
        print(f"📂 Found demo result from file: {demo_result.get('file_info', {}).get('name', 'unknown')}")
        return render_template('analysis.html', result=demo_result)
    
    # No analysis data found
    print("❌ No analysis data found in session, storage, or file")
    print(f"   Session keys: {list(session.keys())}")
    print(f"   Analysis storage keys: {list(analysis_storage.keys())}")
    flash('No analysis data found. Please upload a file first.')
    return redirect(url_for('upload'))

@app.route('/demo')
def demo():
    """Demo page with loading screen and real analysis"""
    # Show loading page immediately
    return render_template('demo_loading.html')

@app.route('/demo_direct')
def demo_direct():
    """Direct demo without loading screen for testing"""
    try:
        print("🔄 Running direct demo with real analysis...")
        start_time = time.time()
        
        # Use comprehensive sample log data for demo (covering 20+ DevOps error categories)
        sample_log_content = """
2025-01-07 10:30:15 ERROR [Database] Connection timeout to database server postgres://localhost:5432
2025-01-07 10:30:16 ERROR [Application] Failed to authenticate user with token invalid_token_123
2025-01-07 10:30:17 WARN [System] High memory usage detected: 95% utilized
2025-01-07 10:30:18 ERROR [Jenkins] Build failed: Maven compilation error in module core-service
2025-01-07 10:30:19 ERROR [Network] Socket timeout on port 8080
2025-01-07 10:30:20 CRITICAL [Security] Unauthorized access attempt from IP *************
2025-01-07 10:30:21 ERROR [CodeCoverage] Coverage threshold not met: 45% (minimum required: 80%)
2025-01-07 10:30:22 ERROR [StaticAnalysis] SonarQube quality gate failed: 15 critical issues found
2025-01-07 10:30:23 CRITICAL [DiskSpace] Disk space exhausted on /var/log: 0 bytes remaining
2025-01-07 10:30:24 ERROR [Monitoring] Prometheus metrics endpoint unreachable: 500 Internal Server Error
2025-01-07 10:30:25 ERROR [Docker] Container 'web-service' failed to start: port 3000 already in use
2025-01-07 10:30:26 ERROR [Kubernetes] Pod 'api-service-abc123' CrashLoopBackOff: container exited with code 1
2025-01-07 10:30:27 ERROR [LoadBalancer] Health check failed for backend server ********:8080
2025-01-07 10:30:28 ERROR [SSL] Certificate expired for domain api.example.com (expired: 2023-12-31)
2025-01-07 10:30:29 ERROR [Backup] Daily backup failed: insufficient permissions to write to /backup/
2025-01-07 10:30:30 ERROR [API] Rate limit exceeded for client IP ************: 1000 requests/hour
2025-01-07 10:30:31 ERROR [Cache] Redis connection lost: NOAUTH Authentication required
2025-01-07 10:30:32 ERROR [Queue] RabbitMQ message processing failed: queue 'tasks' not found
2025-01-07 10:30:33 ERROR [CDN] CloudFront distribution error: origin server timeout
2025-01-07 10:30:34 ERROR [Logging] Log aggregation service down: Elasticsearch cluster unavailable
2025-01-07 10:30:35 ERROR [Jenkins] Pipeline stage 'deploy' failed: script returned exit code 1
2025-01-07 10:30:36 ERROR [SonarQube] Code quality gate FAILED: 23 bugs, 45 vulnerabilities detected
2025-01-07 10:30:37 ERROR [Testing] Unit test suite failed: 15 out of 120 tests failing
2025-01-07 10:30:38 ERROR [Deployment] Helm chart deployment failed: values.yaml validation error
2025-01-07 10:30:39 ERROR [GitLab] CI/CD pipeline failed at stage 'security-scan'
2025-01-07 10:30:40 ERROR [Terraform] Infrastructure provisioning failed: AWS quota exceeded
2025-01-07 10:30:41 ERROR [Artifactory] Maven dependencies download failed: repository unavailable
        """

        # Run the real analysis pipeline
        analysis = root_cause_analyzer.analyze_log_text(sample_log_content)
        
        # Debug: Print all recommendations
        recs = analysis.get('recommendations', [])
        print(f"[DEBUG] Number of recommendations: {len(recs)}", flush=True)
        for idx, rec in enumerate(recs):
            print(f"[DEBUG] Recommendation {idx+1}: {rec.get('problem', rec.get('title', ''))}", flush=True)
        
        # Run error classification on the sample data
        log_lines = [line for line in sample_log_content.split('\n') if line.strip()]
        classified_errors = classify_errors(log_lines)
        
        # Run anomaly detection
        anomalies = anomaly_detector.detect_anomalies([{'message': line} for line in log_lines])
        
        analysis_time = time.time() - start_time
        print(f"✅ Direct demo analysis completed in {analysis_time:.2f} seconds")
        
        result = {
            'success': True,
            'analysis': analysis,
            'classified_errors': classified_errors,
            'root_cause_analysis': analysis,
            'anomalies': anomalies,
            'file_info': {
                'name': 'demo_sample_logs.txt',
                'size': len(sample_log_content.encode('utf-8')),
                'type': 'demo',
                'processed_at': datetime.now().isoformat()
            }
        }
        
        # Store in session for analysis page
        session['analysis_result'] = result
        session.permanent = True  # Make session permanent
        print(f"🔄 Stored direct demo result in session: {result.get('file_info', {}).get('name', 'unknown')}")
        
        return redirect(url_for('analysis_results'))
        
    except Exception as e:
        print(f"❌ Direct demo error: {e}")
        traceback.print_exc()
        
        # Fallback to simplified demo data
        result = get_fallback_demo_data()
        session['analysis_result'] = result
        session.permanent = True  # Make session permanent
        print(f"🔄 Stored fallback demo result in session")
        return redirect(url_for('analysis_results'))

@app.route('/demo/analyze')
def demo_analyze():
    """Run the actual demo analysis"""
    try:
        print("🔄 Running demo with real analysis...")
        start_time = time.time()
        
        # Use comprehensive sample log data for demo (covering 20+ DevOps error categories)
        sample_log_content = """
2025-01-07 10:30:15 ERROR [Database] Connection timeout to database server postgres://localhost:5432
2025-01-07 10:30:16 ERROR [Application] Failed to authenticate user with token invalid_token_123
2025-01-07 10:30:17 WARN [System] High memory usage detected: 95% utilized
2025-01-07 10:30:18 ERROR [Jenkins] Build failed: Maven compilation error in module core-service
2025-01-07 10:30:19 ERROR [Network] Socket timeout on port 8080
2025-01-07 10:30:20 CRITICAL [Security] Unauthorized access attempt from IP *************
2025-01-07 10:30:21 ERROR [CodeCoverage] Coverage threshold not met: 45% (minimum required: 80%)
2025-01-07 10:30:22 ERROR [StaticAnalysis] SonarQube quality gate failed: 15 critical issues found
2025-01-07 10:30:23 CRITICAL [DiskSpace] Disk space exhausted on /var/log: 0 bytes remaining
2025-01-07 10:30:24 ERROR [Monitoring] Prometheus metrics endpoint unreachable: 500 Internal Server Error
2025-01-07 10:30:25 ERROR [Docker] Container 'web-service' failed to start: port 3000 already in use
2025-01-07 10:30:26 ERROR [Kubernetes] Pod 'api-service-abc123' CrashLoopBackOff: container exited with code 1
2025-01-07 10:30:27 ERROR [LoadBalancer] Health check failed for backend server ********:8080
2025-01-07 10:30:28 ERROR [SSL] Certificate expired for domain api.example.com (expired: 2023-12-31)
2025-01-07 10:30:29 ERROR [Backup] Daily backup failed: insufficient permissions to write to /backup/
2025-01-07 10:30:30 ERROR [API] Rate limit exceeded for client IP ************: 1000 requests/hour
2025-01-07 10:30:31 ERROR [Cache] Redis connection lost: NOAUTH Authentication required
2025-01-07 10:30:32 ERROR [Queue] RabbitMQ message processing failed: queue 'tasks' not found
2025-01-07 10:30:33 ERROR [CDN] CloudFront distribution error: origin server timeout
2025-01-07 10:30:34 ERROR [Logging] Log aggregation service down: Elasticsearch cluster unavailable
2025-01-07 10:30:35 ERROR [Jenkins] Pipeline stage 'deploy' failed: script returned exit code 1
2025-01-07 10:30:36 ERROR [SonarQube] Code quality gate FAILED: 23 bugs, 45 vulnerabilities detected
2025-01-07 10:30:37 ERROR [Testing] Unit test suite failed: 15 out of 120 tests failing
2025-01-07 10:30:38 ERROR [Deployment] Helm chart deployment failed: values.yaml validation error
2025-01-07 10:30:39 ERROR [GitLab] CI/CD pipeline failed at stage 'security-scan'
2025-01-07 10:30:40 ERROR [Terraform] Infrastructure provisioning failed: AWS quota exceeded
2025-01-07 10:30:41 ERROR [Artifactory] Maven dependencies download failed: repository unavailable
        """

        # Run the real analysis pipeline
        analysis = root_cause_analyzer.analyze_log_text(sample_log_content)
        
        # Debug: Print all recommendations
        recs = analysis.get('recommendations', [])
        print(f"[DEBUG] Number of recommendations: {len(recs)}", flush=True)
        for idx, rec in enumerate(recs):
            print(f"[DEBUG] Recommendation {idx+1}: {rec.get('problem', rec.get('title', ''))}", flush=True)
        
        # Run error classification on the sample data
        log_lines = [line for line in sample_log_content.split('\n') if line.strip()]
        classified_errors = classify_errors(log_lines)
        
        # Run anomaly detection
        anomalies = anomaly_detector.detect_anomalies([{'message': line} for line in log_lines])
        
        analysis_time = time.time() - start_time
        print(f"✅ Demo analysis completed in {analysis_time:.2f} seconds")
        
        result = {
            'success': True,
            'analysis': analysis,
            'classified_errors': classified_errors,
            'root_cause_analysis': analysis,  # For backward compatibility
            'anomalies': anomalies,
            'file_info': {
                'name': 'demo_sample_logs.txt',
                'size': len(sample_log_content.encode('utf-8')),
                'type': 'demo',
                'processed_at': datetime.now().isoformat()
            }
        }
        
        # Store in session for analysis page
        session['analysis_result'] = result
        session.permanent = True  # Make session permanent
        print(f"🔄 Stored analysis result in session: {result.get('file_info', {}).get('name', 'unknown')}")
        print(f"   Session ID: {session.get('_id', 'no-id')}")
        
        # Also save to file for persistence across server restarts
        save_demo_result(result)
        
        return jsonify({'success': True, 'redirect': '/analysis_results'})
        
    except Exception as e:
        print(f"❌ Demo error: {e}")
        traceback.print_exc()
        
        # Fallback to simplified demo data if real analysis fails
        result = get_fallback_demo_data()
        session['analysis_result'] = result
        session.permanent = True
        save_demo_result(result)
        return jsonify({'success': True, 'redirect': '/analysis_results'})

def get_fallback_demo_data():
    """Fallback demo data if real analysis fails"""
    return {
        'success': True,
        'analysis': {
            'summary': 'Demo analysis using sample data (fallback mode)',
            'errors_found': 10,
            'log_count': 14,
            'severity': 'critical',
            'analysis_duration_ms': 50,
            'recommendations': [
                {
                    'cause': 'resource_exhaustion',
                    'problem': 'Memory Exhaustion Issue',
                    'description': 'The application is running out of heap memory, causing OutOfMemoryError exceptions.',
                    'steps': [
                        'Increase JVM heap size (-Xmx parameter)',
                        'Analyze memory usage patterns',
                        'Implement memory monitoring and alerts'
                    ],
                    'severity': 'critical',
                    'source': 'ai_generated'
                },
                {
                    'cause': 'network_connectivity',
                    'problem': 'Network Connectivity Issues',
                    'description': 'Multiple connection timeouts detected indicating network or service availability problems.',
                    'steps': [
                        'Check network connectivity to external services',
                        'Verify firewall and security group settings',
                        'Implement connection retry mechanisms'
                    ],
                    'severity': 'high',
                    'source': 'ai_generated'
                },
                {
                    'cause': 'sonarqube_quality_gate',
                    'problem': 'SonarQube Quality Gate Failed',
                    'description': 'The SonarQube analysis did not pass the quality gate. Code issues or coverage thresholds were not met.',
                    'steps': [
                        'Review the SonarQube report for failed conditions',
                        'Fix code smells, bugs, and vulnerabilities',
                        'Increase code coverage as required',
                        'Re-run the pipeline after fixes'
                    ],
                    'severity': 'high',
                    'source': 'ai_generated'
                },
                {
                    'cause': 'jenkins_pipeline_failure',
                    'problem': 'Jenkins Pipeline Failure',
                    'description': 'A Jenkins pipeline stage failed, possibly due to test failures, script errors, or environment issues.',
                    'steps': [
                        'Check the Jenkins console output for error details',
                        'Review failed stages and logs',
                        'Fix any script or environment issues',
                        'Re-run the pipeline'
                    ],
                    'severity': 'high',
                    'source': 'ai_generated'
                },
                {
                    'cause': 'static_analysis_error',
                    'problem': 'Static Analysis Tool Error',
                    'description': 'Static code analysis found critical issues or failed to complete.',
                    'steps': [
                        'Review static analysis tool output (e.g., SonarQube, pylint)',
                        'Fix reported code issues',
                        'Ensure the tool is configured correctly in the pipeline'
                    ],
                    'severity': 'medium',
                    'source': 'ai_generated'
                },
                {
                    'cause': 'code_coverage_drop',
                    'problem': 'Code Coverage Below Threshold',
                    'description': 'Automated tests did not meet the required code coverage threshold.',
                    'steps': [
                        'Add or improve unit/integration tests',
                        'Check coverage reports for untested code',
                        'Set appropriate coverage thresholds in CI/CD'
                    ],
                    'severity': 'medium',
                    'source': 'ai_generated'
                }
            ],
            'patterns': {
                'technology_detected': ['Java', 'MySQL', 'SSL', 'Spring', 'SonarQube', 'Jenkins', 'Go'],
                'common_issues': [
                    'connection_timeout', 'memory_exhaustion', 'authentication_failure',
                    'sonarqube_quality_gate', 'jenkins_pipeline_failure', 'static_analysis_error', 'code_coverage_drop'
                ]
            },
            'top_errors': [
                {
                    'message': 'OutOfMemoryError: Java heap space',
                    'category': 'memory_errors',
                    'severity': 'critical',
                    'frequency': 1,
                    'line_number': 2
                },
                {
                    'message': 'Failed to connect to Redis server: Connection timeout',
                    'category': 'connection_errors',
                    'severity': 'high',
                    'frequency': 1,
                    'line_number': 1
                },
                {
                    'message': 'SonarQube Quality Gate failed: 2 conditions not met',
                    'category': 'sonarqube_quality_gate',
                    'severity': 'high',
                    'frequency': 1,
                    'line_number': 5
                },
                {
                    'message': 'Jenkins pipeline failed at stage: Deploy',
                    'category': 'jenkins_pipeline_failure',
                    'severity': 'high',
                    'frequency': 1,
                    'line_number': 6
                },
                {
                    'message': 'Static analysis failed: 3 critical issues found',
                    'category': 'static_analysis_error',
                    'severity': 'medium',
                    'frequency': 1,
                    'line_number': 7
                },
                {
                    'message': 'Go coverage check failed: coverage 68% < required 80%',
                    'category': 'code_coverage_drop',
                    'severity': 'medium',
                    'frequency': 1,
                    'line_number': 8
                },
                {
                    'message': 'Authentication failed: Invalid credentials',
                    'category': 'authentication_errors',
                    'severity': 'high',
                    'frequency': 1,
                    'line_number': 3
                },
                {
                    'message': 'HTTP 500 Internal Server Error',
                    'category': 'network_errors',
                    'severity': 'high',
                    'frequency': 1,
                    'line_number': 4
                },
                {
                    'message': 'Failed to process user request: NullPointerException',
                    'category': 'application_errors',
                    'severity': 'high',
                    'frequency': 1,
                    'line_number': 9
                }
            ]
        },
        'classified_errors': {
            'categories': {
                'memory_errors': ['OutOfMemoryError: Java heap space'],
                'connection_errors': ['Failed to connect to Redis server: Connection timeout'],
                'sonarqube_quality_gate': ['SonarQube Quality Gate failed: 2 conditions not met'],
                'jenkins_pipeline_failure': ['Jenkins pipeline failed at stage: Deploy'],
                'static_analysis_error': ['Static analysis failed: 3 critical issues found'],
                'code_coverage_drop': ['Go coverage check failed: coverage 68% < required 80%'],
                'authentication_errors': ['Authentication failed: Invalid credentials'],
                'network_errors': ['HTTP 500 Internal Server Error'],
                'application_errors': ['Failed to process user request: NullPointerException']
            },
            'total_errors': 9,
            'summary': {
                'memory_errors': 1,
                'connection_errors': 1,
                'sonarqube_quality_gate': 1,
                'jenkins_pipeline_failure': 1,
                'static_analysis_error': 1,
                'code_coverage_drop': 1,
                'authentication_errors': 1,
                'network_errors': 1,
                'application_errors': 1
            }
        },
        'anomalies': {
            'anomalies_detected': [
                {
                    'message': 'High error rate detected',
                    'severity': 'high',
                    'timestamp': '2025-01-07T10:30:21'
                }
            ],
            'total_anomalies': 1
        },
        'file_info': {
            'name': 'demo_sample_logs.txt',
            'size': 2048,
            'type': 'demo',
            'processed_at': datetime.now().isoformat()
        }
    }

@app.route('/api/analyze', methods=['POST'])
def api_analyze():
    """API endpoint for log analysis"""
    try:
        data = request.get_json()
        log_text = data.get('log_text', '')
        
        if not log_text:
            return jsonify({'error': 'No log text provided'}), 400
        
        # Run complete analysis pipeline
        analysis = root_cause_analyzer.analyze_log_text(log_text)
        
        # Run error classification
        log_lines = [line for line in log_text.split('\n') if line.strip()]
        classified_errors = classify_errors(log_lines)
        
        # Run anomaly detection
        anomalies = anomaly_detector.detect_anomalies([{'message': line} for line in log_lines])
        
        return jsonify({
            'success': True,
            'analysis': analysis,
            'classified_errors': classified_errors,
            'root_cause_analysis': analysis,
            'anomalies': anomalies
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/rag/stats')
def rag_stats():
    """Get RAG system statistics"""
    try:
        stats = root_cause_analyzer.get_rag_stats()
        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/rag/add-knowledge', methods=['POST'])
def add_rag_knowledge():
    """Add new knowledge to the RAG system"""
    try:
        data = request.get_json()
        
        required_fields = ['title', 'problem', 'solution_steps', 'category', 'technologies']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400
        
        root_cause_analyzer.add_successful_resolution(
            error_description=data['problem'],
            solution_title=data['title'],
            solution_steps=data['solution_steps'],
            category=data['category'],
            technologies=data['technologies'],
            resolution_time=data.get('resolution_time', 'unknown')
        )
        
        return jsonify({
            'success': True,
            'message': 'Knowledge added successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    print("Starting DevOps AI Log Analysis Flask Application...")
    print("Available routes:")
    for rule in app.url_map.iter_rules():
        print(f"  {rule.rule} -> {rule.endpoint}")
    
    app.run(host='0.0.0.0', port=5001, debug=True)
