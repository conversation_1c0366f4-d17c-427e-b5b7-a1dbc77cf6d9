<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Interface - DevOps AI Log Analysis</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .chat-container {
            display: flex;
            height: 100vh;
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }

        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            background: #2c3e50;
            color: white;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
            padding: 8px 12px;
            background: rgba(255,255,255,0.1);
            border-radius: 6px;
            font-size: 0.8rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
        }

        .status-dot.offline {
            background: #e74c3c;
        }

        .examples-section {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }

        .examples-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .example-query {
            background: white;
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.9rem;
        }

        .example-query:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .history-section {
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }

        .history-item {
            padding: 8px;
            margin-bottom: 5px;
            background: white;
            border-radius: 4px;
            border-left: 3px solid #3498db;
            font-size: 0.8rem;
        }

        .main-chat {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 20px;
            background: white;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .chat-header h1 {
            color: #2c3e50;
            font-size: 1.5rem;
        }

        .chat-header .clear-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            max-width: 80%;
        }

        .message.user {
            margin-left: auto;
        }

        .message.assistant {
            margin-right: auto;
        }

        .message-content {
            padding: 15px;
            border-radius: 12px;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: #2196f3;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #2c3e50;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
        }

        .message-time {
            font-size: 0.8rem;
            opacity: 0.6;
            margin-top: 5px;
        }

        .message.user .message-time {
            text-align: right;
        }

        .results-summary {
            background: #e8f5e8;
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #27ae60;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }

        .result-card {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            font-size: 0.9rem;
        }

        .result-card strong {
            color: #2c3e50;
        }

        .insights-list {
            margin: 10px 0;
        }

        .insights-list li {
            margin: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .insights-list li:before {
            content: "💡";
            position: absolute;
            left: 0;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-group {
            flex: 1;
            position: relative;
        }

        .chat-textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 1rem;
            font-family: inherit;
            resize: none;
            min-height: 50px;
            max-height: 120px;
            transition: border-color 0.2s;
        }

        .chat-textarea:focus {
            outline: none;
            border-color: #2196f3;
        }

        .send-btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.2s;
            height: 50px;
        }

        .send-btn:hover {
            background: #1976d2;
        }

        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .suggestions-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
        }

        .suggestion-item {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            font-size: 0.9rem;
        }

        .suggestion-item:hover {
            background: #f8f9fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            padding: 10px;
            color: #6c757d;
            font-style: italic;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #6c757d;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 6px;
            border-left: 4px solid #f44336;
            margin: 10px 0;
        }

        .welcome-message {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .welcome-message h2 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .welcome-message p {
            margin-bottom: 20px;
        }

        .feature-badges {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .feature-badge {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .chat-container {
                flex-direction: column;
                height: 100vh;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
                max-height: 200px;
            }
            
            .examples-section {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🤖 AI Chat Assistant</h2>
                <p>Ask questions about your logs in natural language</p>
                <div class="status-indicator">
                    <span class="status-dot" id="statusDot"></span>
                    <span id="statusText">Connecting...</span>
                </div>
            </div>
            
            <div class="examples-section">
                <h3>💭 Example Queries</h3>
                <div id="exampleQueries"></div>
            </div>
            
            <div class="history-section">
                <h3>📜 Recent Queries</h3>
                <div id="queryHistory"></div>
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="main-chat">
            <div class="chat-header">
                <h1>🔍 Natural Language Log Analysis</h1>
                <button class="clear-btn" onclick="clearChat()">Clear Chat</button>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    <h2>Welcome to AI-Powered Log Analysis!</h2>
                    <p>Ask me anything about your logs using natural language. I can help you find errors, analyze patterns, and investigate issues.</p>
                    <div class="feature-badges">
                        <span class="feature-badge">🔍 Smart Search</span>
                        <span class="feature-badge">🤖 AI Analysis</span>
                        <span class="feature-badge">📊 Insights</span>
                        <span class="feature-badge">⚡ Real-time</span>
                    </div>
                </div>
            </div>

            <div class="typing-indicator" id="typingIndicator">
                <span>AI is thinking</span>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>

            <div class="chat-input">
                <div class="input-container">
                    <div class="input-group">
                        <textarea 
                            class="chat-textarea" 
                            id="queryInput" 
                            placeholder="Ask me about your logs... (e.g., 'Show me all errors in the last hour')"
                            rows="1"
                        ></textarea>
                        <div class="suggestions-dropdown" id="suggestionsDropdown"></div>
                    </div>
                    <button class="send-btn" id="sendBtn" onclick="sendQuery()">
                        📤 Send
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let chatHistory = [];
        let currentSuggestions = [];
        let isProcessing = false;

        // Initialize the chat interface
        document.addEventListener('DOMContentLoaded', function() {
            checkStatus();
            loadExamples();
            loadHistory();
            setupEventListeners();
        });

        function setupEventListeners() {
            const input = document.getElementById('queryInput');
            const sendBtn = document.getElementById('sendBtn');
            
            // Auto-resize textarea
            input.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                
                // Get suggestions
                if (this.value.length > 2) {
                    getSuggestions(this.value);
                } else {
                    hideSuggestions();
                }
            });

            // Send on Enter (but not Shift+Enter)
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendQuery();
                }
            });

            // Hide suggestions when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.input-group')) {
                    hideSuggestions();
                }
            });
        }

        async function checkStatus() {
            try {
                const response = await fetch('/api/chat/status');
                const status = await response.json();
                
                const statusDot = document.getElementById('statusDot');
                const statusText = document.getElementById('statusText');
                
                if (status.search_engine_available) {
                    statusDot.classList.remove('offline');
                    statusText.textContent = status.llm_available ? 'AI Ready' : 'Pattern Mode';
                } else {
                    statusDot.classList.add('offline');
                    statusText.textContent = 'Offline';
                }
            } catch (error) {
                console.error('Status check failed:', error);
                document.getElementById('statusDot').classList.add('offline');
                document.getElementById('statusText').textContent = 'Error';
            }
        }

        async function loadExamples() {
            try {
                const response = await fetch('/api/chat/examples');
                const data = await response.json();
                
                const container = document.getElementById('exampleQueries');
                container.innerHTML = '';
                
                data.examples.slice(0, 6).forEach(example => {
                    const div = document.createElement('div');
                    div.className = 'example-query';
                    div.textContent = example;
                    div.onclick = () => {
                        document.getElementById('queryInput').value = example;
                        sendQuery();
                    };
                    container.appendChild(div);
                });
            } catch (error) {
                console.error('Failed to load examples:', error);
            }
        }

        async function loadHistory() {
            try {
                const response = await fetch('/api/chat/history?limit=5');
                const data = await response.json();
                
                const container = document.getElementById('queryHistory');
                container.innerHTML = '';
                
                data.history.forEach(item => {
                    const div = document.createElement('div');
                    div.className = 'history-item';
                    div.innerHTML = `
                        <div style="font-weight: 500;">${item.query}</div>
                        <div style="font-size: 0.7rem; opacity: 0.7;">${item.result_count} results</div>
                    `;
                    div.onclick = () => {
                        document.getElementById('queryInput').value = item.query;
                        sendQuery();
                    };
                    container.appendChild(div);
                });
            } catch (error) {
                console.error('Failed to load history:', error);
            }
        }

        async function getSuggestions(query) {
            try {
                const response = await fetch(`/api/chat/suggestions?q=${encodeURIComponent(query)}`);
                const data = await response.json();
                
                showSuggestions(data.suggestions);
            } catch (error) {
                console.error('Failed to get suggestions:', error);
            }
        }

        function showSuggestions(suggestions) {
            const dropdown = document.getElementById('suggestionsDropdown');
            dropdown.innerHTML = '';
            
            if (suggestions.length === 0) {
                dropdown.style.display = 'none';
                return;
            }
            
            suggestions.forEach(suggestion => {
                const div = document.createElement('div');
                div.className = 'suggestion-item';
                div.textContent = suggestion;
                div.onclick = () => {
                    document.getElementById('queryInput').value = suggestion;
                    hideSuggestions();
                    sendQuery();
                };
                dropdown.appendChild(div);
            });
            
            dropdown.style.display = 'block';
        }

        function hideSuggestions() {
            document.getElementById('suggestionsDropdown').style.display = 'none';
        }

        async function sendQuery() {
            const input = document.getElementById('queryInput');
            const query = input.value.trim();
            
            if (!query || isProcessing) {
                return;
            }
            
            isProcessing = true;
            updateSendButton(true);
            showTypingIndicator();
            
            // Add user message
            addMessage('user', query);
            
            // Clear input
            input.value = '';
            input.style.height = 'auto';
            hideSuggestions();
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query: query })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addResultMessage(data.results);
                    if (data.cached) {
                        addMessage('assistant', '💾 Results retrieved from cache for faster response.');
                    }
                } else {
                    addMessage('assistant', `❌ Error: ${data.error}`);
                }
                
                // Refresh history
                loadHistory();
                
            } catch (error) {
                console.error('Query failed:', error);
                addMessage('assistant', `❌ Network error: ${error.message}`);
            } finally {
                hideTypingIndicator();
                updateSendButton(false);
                isProcessing = false;
            }
        }

        function addMessage(sender, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.innerHTML = content;
            
            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleTimeString();
            
            messageDiv.appendChild(contentDiv);
            messageDiv.appendChild(timeDiv);
            messagesContainer.appendChild(messageDiv);
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function addResultMessage(results) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            let html = `
                <div class="results-summary">
                    <h4>📊 Search Results</h4>
                    <div class="results-grid">
                        <div class="result-card">
                            <strong>Total Results:</strong> ${results.total_results}
                        </div>
                        <div class="result-card">
                            <strong>Confidence:</strong> ${(results.confidence * 100).toFixed(1)}%
                        </div>
                        <div class="result-card">
                            <strong>Processing:</strong> ${results.processing_method}
                        </div>
                        <div class="result-card">
                            <strong>Time:</strong> ${results.execution_time_ms}ms
                        </div>
                    </div>
                </div>
            `;
            
            if (results.insights && results.insights.length > 0) {
                html += `
                    <div style="margin: 15px 0;">
                        <h4>💡 Insights</h4>
                        <ul class="insights-list">
                            ${results.insights.map(insight => `<li>${insight}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
            
            if (results.analysis && results.analysis.severity_distribution) {
                html += `
                    <div style="margin: 15px 0;">
                        <h4>📈 Analysis</h4>
                        <div class="results-grid">
                            <div class="result-card">
                                <strong>Severity Distribution:</strong><br>
                                ${Object.entries(results.analysis.severity_distribution)
                                    .map(([k, v]) => `${k}: ${v}`)
                                    .join(', ')}
                            </div>
                            ${results.analysis.anomalies_found > 0 ? `
                                <div class="result-card">
                                    <strong>Anomalies Found:</strong> ${results.analysis.anomalies_found}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            }
            
            if (results.results && results.results.length > 0) {
                html += `
                    <div style="margin: 15px 0;">
                        <h4>📝 Sample Log Entries</h4>
                        ${results.results.slice(0, 3).map(log => `
                            <div class="result-card" style="margin: 5px 0;">
                                <strong>${log.timestamp || 'Unknown time'}</strong> 
                                [${log.level || 'INFO'}] 
                                ${log.service ? `[${log.service}] ` : ''}
                                ${log.message || log.raw_log || 'No message'}
                            </div>
                        `).join('')}
                        ${results.results.length > 3 ? `
                            <div style="font-size: 0.9rem; color: #6c757d; margin-top: 10px;">
                                ... and ${results.results.length - 3} more entries
                            </div>
                        ` : ''}
                    </div>
                `;
            }
            
            contentDiv.innerHTML = html;
            
            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleTimeString();
            
            messageDiv.appendChild(contentDiv);
            messageDiv.appendChild(timeDiv);
            messagesContainer.appendChild(messageDiv);
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'flex';
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        function updateSendButton(processing) {
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = processing;
            sendBtn.textContent = processing ? '⏳ Processing...' : '📤 Send';
        }

        async function clearChat() {
            if (confirm('Clear chat history and cache?')) {
                try {
                    await fetch('/api/chat/clear', { method: 'POST' });
                    
                    // Clear UI
                    document.getElementById('chatMessages').innerHTML = `
                        <div class="welcome-message">
                            <h2>Chat Cleared! 🧹</h2>
                            <p>Start a new conversation by asking about your logs.</p>
                        </div>
                    `;
                    
                    // Reload history
                    loadHistory();
                    
                } catch (error) {
                    console.error('Failed to clear chat:', error);
                }
            }
        }
    </script>
</body>
</html>
