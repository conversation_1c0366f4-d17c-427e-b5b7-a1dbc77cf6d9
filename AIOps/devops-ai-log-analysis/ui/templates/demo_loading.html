<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Processing Demo - DevOps AI Log Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #f8fafc;
            --accent-color: #10b981;
        }
        
        body {
            background-color: var(--secondary-color);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .loading-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading-card {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .loading-spinner {
            width: 80px;
            height: 80px;
            border: 6px solid #e2e8f0;
            border-top: 6px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 2rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .progress {
            height: 8px;
            border-radius: 4px;
            background: #e2e8f0;
            margin: 2rem 0;
        }
        
        .progress-bar {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .step-list {
            text-align: left;
            margin-top: 2rem;
        }
        
        .step-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }
        
        .step-item.active {
            opacity: 1;
            color: var(--primary-color);
        }
        
        .step-item.completed {
            opacity: 1;
            color: var(--accent-color);
        }
        
        .step-icon {
            width: 24px;
            height: 24px;
            margin-right: 1rem;
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="loading-card">
            <div class="loading-spinner"></div>
            <h3 class="fw-bold mb-3">Running AI Analysis</h3>
            <p class="text-muted mb-4">
                Processing your log data through our advanced AI pipeline...
            </p>
            
            <div class="progress">
                <div class="progress-bar" id="progressBar" style="width: 0%"></div>
            </div>
            
            <div class="step-list">
                <div class="step-item" id="step1">
                    <i class="fas fa-file-text step-icon"></i>
                    <span>Ingesting log data</span>
                </div>
                <div class="step-item" id="step2">
                    <i class="fas fa-tags step-icon"></i>
                    <span>Classifying errors</span>
                </div>
                <div class="step-item" id="step3">
                    <i class="fas fa-search-plus step-icon"></i>
                    <span>Detecting anomalies</span>
                </div>
                <div class="step-item" id="step4">
                    <i class="fas fa-robot step-icon"></i>
                    <span>Generating AI recommendations</span>
                </div>
                <div class="step-item" id="step5">
                    <i class="fas fa-chart-bar step-icon"></i>
                    <span>Preparing results</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simulate analysis progress
        let progress = 0;
        let currentStep = 1;
        
        const steps = [
            { id: 'step1', duration: 1000 },
            { id: 'step2', duration: 2000 },
            { id: 'step3', duration: 2000 },
            { id: 'step4', duration: 4000 }, // Longest for LLM processing
            { id: 'step5', duration: 1000 }
        ];
        
        function updateProgress() {
            const progressBar = document.getElementById('progressBar');
            const stepElement = document.getElementById(`step${currentStep}`);
            
            // Mark current step as active
            stepElement.classList.add('active');
            
            const stepDuration = steps[currentStep - 1].duration;
            const increment = 20 / stepDuration * 100; // 20% per step
            
            const interval = setInterval(() => {
                progress += increment;
                progressBar.style.width = progress + '%';
                
                if (progress >= currentStep * 20) {
                    clearInterval(interval);
                    
                    // Mark step as completed
                    stepElement.classList.remove('active');
                    stepElement.classList.add('completed');
                    stepElement.querySelector('i').className = 'fas fa-check-circle step-icon';
                    
                    currentStep++;
                    
                    if (currentStep <= 5) {
                        updateProgress();
                    } else {
                        // Analysis complete, redirect after a brief delay
                        setTimeout(() => {
                            window.location.href = '/analysis_results';
                        }, 500);
                    }
                }
            }, 100);
        }
        
        // Start the progress simulation
        setTimeout(updateProgress, 500);
        
        // Start the actual analysis in the background
        setTimeout(() => {
            fetch('/demo/analyze')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.redirect) {
                        window.location.href = data.redirect;
                    } else {
                        console.error('Analysis failed:', data);
                        window.location.href = '/analysis_results';
                    }
                })
                .catch(error => {
                    console.error('Analysis error:', error);
                    window.location.href = '/analysis_results';
                });
        }, 1000);
    </script>
</body>
</html>
