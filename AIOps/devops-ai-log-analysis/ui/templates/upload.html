<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Logs - DevOps AI Log Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #f8fafc;
            --accent-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
        }
        
        body {
            background-color: var(--secondary-color);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .upload-container {
            background: white;
            border-radius: 16px;
            padding: 3rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            margin-top: 2rem;
        }
        
        .upload-zone {
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            padding: 3rem;
            text-align: center;
            background: #f8fafc;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .upload-zone:hover {
            border-color: var(--primary-color);
            background: #f1f5f9;
        }
        
        .upload-zone.dragover {
            border-color: var(--primary-color);
            background: #e0f2fe;
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #1e40af, var(--primary-color));
            transform: translateY(-1px);
        }
        
        .format-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.2s;
        }
        
        .format-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .progress-container {
            display: none;
            margin-top: 2rem;
        }
        
        .alert {
            border-radius: 8px;
            border: none;
            padding: 1rem 1.5rem;
            font-weight: 500;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #fef2f2, #fecaca);
            color: #991b1b;
        }
        
        .file-preview {
            display: none;
            margin-top: 1rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .file-info i {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-robot me-2"></i>DevOps AI Log Analysis
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link active" href="/upload">Upload Logs</a>
                <a class="nav-link" href="/demo">Demo</a>
            </div>
        </div>
    </nav>

    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="upload-container">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold mb-3">Upload Log Files</h2>
                        <p class="text-muted">
                            Upload your log files for AI-powered analysis and root cause detection.
                            Maximum file size: 16MB
                        </p>
                    </div>

                    {% with messages = get_flashed_messages() %}
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-info alert-dismissible fade show" role="alert">
                                    <i class="fas fa-info-circle me-2"></i>{{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form method="post" enctype="multipart/form-data" id="uploadForm">
                        <div class="upload-zone" id="uploadZone">
                            <input type="file" name="file" class="file-input" id="fileInput" accept=".txt,.log,.json,.xml,.csv" required>
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <h5 class="fw-bold mb-2">Drop your log files here</h5>
                                <p class="text-muted mb-3">or click to browse</p>
                                <div class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Select Files
                                </div>
                            </div>
                        </div>

                        <div class="file-preview" id="filePreview">
                            <div class="file-info">
                                <i class="fas fa-file-alt fa-2x"></i>
                                <div>
                                    <div class="fw-bold" id="fileName"></div>
                                    <div class="text-muted small" id="fileSize"></div>
                                </div>
                            </div>
                        </div>

                        <div class="progress-container" id="progressContainer">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="text-center mt-2">
                                <small class="text-muted">Processing your log file...</small>
                            </div>
                        </div>

                        <div class="d-flex gap-3 justify-content-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg" id="uploadBtn" disabled>
                                <i class="fas fa-upload me-2"></i>Upload & Analyze
                            </button>
                            <a href="/demo" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-play me-2"></i>Try Demo Instead
                            </a>
                        </div>
                    </form>
                </div>

                <div class="mt-4">
                    <h4 class="fw-bold mb-3">Supported File Formats</h4>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="format-card">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file-alt fa-2x text-primary me-3"></i>
                                    <div>
                                        <h6 class="fw-bold mb-1">Plain Text (.txt, .log)</h6>
                                        <small class="text-muted">Standard log files with multiline support</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="format-card">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-code fa-2x text-success me-3"></i>
                                    <div>
                                        <h6 class="fw-bold mb-1">JSON (.json)</h6>
                                        <small class="text-muted">Structured JSON log files</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="format-card">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file-code fa-2x text-warning me-3"></i>
                                    <div>
                                        <h6 class="fw-bold mb-1">XML (.xml)</h6>
                                        <small class="text-muted">XML formatted log files</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="format-card">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-table fa-2x text-info me-3"></i>
                                    <div>
                                        <h6 class="fw-bold mb-1">CSV (.csv)</h6>
                                        <small class="text-muted">Comma-separated values</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');
        const filePreview = document.getElementById('filePreview');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const uploadBtn = document.getElementById('uploadBtn');
        const uploadForm = document.getElementById('uploadForm');
        const progressContainer = document.getElementById('progressContainer');

        // File input change handler
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                displayFileInfo(file);
                uploadBtn.disabled = false;
            }
        });

        // Drag and drop handlers
        uploadZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                displayFileInfo(files[0]);
                uploadBtn.disabled = false;
            }
        });

        function displayFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = `${(file.size / 1024).toFixed(2)} KB`;
            filePreview.style.display = 'block';
        }

        // Form submission handler
        uploadForm.addEventListener('submit', function(e) {
            if (!fileInput.files[0]) {
                e.preventDefault();
                alert('Please select a file first.');
                return;
            }

            // Show progress
            progressContainer.style.display = 'block';
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';

            // Simulate progress (in real implementation, this would be actual progress)
            let progress = 0;
            const progressBar = document.querySelector('.progress-bar');
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                progressBar.style.width = progress + '%';
                
                if (progress >= 90) {
                    clearInterval(interval);
                }
            }, 200);
        });

        // Reset form on page load
        window.addEventListener('load', function() {
            uploadForm.reset();
            filePreview.style.display = 'none';
            progressContainer.style.display = 'none';
            uploadBtn.disabled = true;
        });
    </script>
</body>
</html>
