<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analysis Results - DevOps AI Log Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #f8fafc;
            --accent-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
        }
        
        body {
            background-color: var(--secondary-color);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .analysis-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #fff, #f8fafc);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: transform 0.2s;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
        }
        
        .metric-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .priority-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .priority-critical {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .priority-high {
            background: #fef3c7;
            color: #92400e;
        }
        
        .priority-medium {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .priority-low {
            background: #d1fae5;
            color: #065f46;
        }
        
        .progress-bar {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
        }
        
        .error-category {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .error-category h6 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .error-list {
            max-height: 200px;
            overflow-y: auto;
            font-size: 0.9rem;
        }
        
        .recommendation-card {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border: 1px solid #0284c7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .recommendation-card h6 {
            color: #0369a1;
        }
        
        .anomaly-item {
            background: #fef2f2;
            border: 1px solid #fca5a5;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .anomaly-item h6 {
            color: #991b1b;
        }
        
        .file-info-card {
            background: linear-gradient(135deg, var(--accent-color), #059669);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #1e40af, var(--primary-color));
            transform: translateY(-1px);
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 2rem;
        }
        
        .tab-content {
            padding: 2rem 0;
        }
        
        .nav-tabs .nav-link {
            border: none;
            border-bottom: 2px solid transparent;
            color: #6b7280;
            font-weight: 500;
        }
        
        .nav-tabs .nav-link.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }
        
        .alert {
            border-radius: 8px;
            border: none;
            padding: 1rem 1.5rem;
            font-weight: 500;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
        }
        
        .alert-info {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
        }
        
        .stack-trace-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            line-height: 1.5;
            overflow-x: auto;
            white-space: pre-wrap;
            border: 1px solid #334155;
        }
        
        .stack-trace-block code {
            color: #e2e8f0;
            background: transparent;
            padding: 0;
            font-size: inherit;
        }
        
        .anomaly-details {
            border-top: 1px solid #e5e7eb;
            padding-top: 1rem;
        }
        
        .anomaly-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .anomaly-item:last-child {
            margin-bottom: 0;
        }
        
        .solution-item {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: 0 4px 4px 0;
        }
        
        .cause-item {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: 0 4px 4px 0;
        }
        
        .prevention-item {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: 0 4px 4px 0;
        }
        
        .implementation-note {
            background: #e2e3e5;
            border-left: 4px solid #6c757d;
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: 0 4px 4px 0;
        }
        
        .related-errors {
            background: #fffbeb;
            border-radius: 8px;
            padding: 0.75rem;
        }
        
        .related-errors .alert {
            margin-bottom: 0.5rem;
            padding: 0.75rem;
            font-size: 0.9rem;
        }
        
        .related-errors .alert:last-child {
            margin-bottom: 0;
        }
        
        .related-errors code {
            font-size: 0.85rem;
            word-break: break-word;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-robot me-2"></i>DevOps AI Log Analysis
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link" href="/upload">Upload Logs</a>
                <a class="nav-link" href="/demo">Demo</a>
                <a class="nav-link" href="/clear_session">Clear Session</a>
                <a class="nav-link" href="/debug">Debug</a>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>{{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="file-info-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="fw-bold mb-2">
                                <i class="fas fa-file-alt me-2"></i>{{ result.file_info.name }}
                            </h4>
                            <p class="mb-0">
                                <i class="fas fa-clock me-2"></i>Processed: {{ result.file_info.processed_at }}
                                <span class="ms-3">
                                    <i class="fas fa-hdd me-2"></i>Size: {{ "%.2f"|format(result.file_info.size / 1024) }} KB
                                </span>
                            </p>
                        </div>
                        <div class="text-end">
                            <button class="btn btn-light" onclick="window.location.href='/upload'">
                                <i class="fas fa-upload me-2"></i>Upload New File
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% set analysis = result.analysis if result.analysis is defined else result.root_cause_analysis %}

        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-number">{{ analysis.errors_found if analysis.errors_found is defined else result.classified_errors.total_errors }}</div>
                    <div class="text-muted">Total Errors</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-number">{{ analysis.patterns.technology_detected|length if analysis.patterns is defined and analysis.patterns.technology_detected is defined else result.classified_errors.categories|length }}</div>
                    <div class="text-muted">Error Categories</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-number">{{ analysis.errors_found if analysis.errors_found is defined else (result.anomalies.total_anomalies if result.anomalies.total_anomalies is defined else 0) }}</div>
                    <div class="text-muted">Issues Found</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-number">{{ analysis.recommendations|length }}</div>
                    <div class="text-muted">Recommendations</div>
                </div>
            </div>
        </div>

        <!-- Analysis Tabs -->
        <div class="analysis-card">
            <ul class="nav nav-tabs" id="analysisTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="root-cause-tab" data-bs-toggle="tab" data-bs-target="#root-cause" type="button" role="tab">
                        <i class="fas fa-bullseye me-2"></i>Root Cause Analysis
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="errors-tab" data-bs-toggle="tab" data-bs-target="#errors" type="button" role="tab">
                        <i class="fas fa-exclamation-triangle me-2"></i>Error Classification
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="anomalies-tab" data-bs-toggle="tab" data-bs-target="#anomalies" type="button" role="tab">
                        <i class="fas fa-search-plus me-2"></i>Anomaly Detection
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="charts-tab" data-bs-toggle="tab" data-bs-target="#charts" type="button" role="tab">
                        <i class="fas fa-chart-bar me-2"></i>Visual Analysis
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="all-errors-tab" data-bs-toggle="tab" data-bs-target="#all-errors" type="button" role="tab">
                        <i class="fas fa-list me-2"></i>All Extracted Errors
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="analysisTabContent">
                <!-- Root Cause Analysis Tab -->
                <div class="tab-pane fade show active" id="root-cause" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="fw-bold mb-3">Root Cause Summary</h5>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                {{ analysis.summary }}
                            </div>

                            {% if analysis.most_common_cause is defined and analysis.most_common_cause %}
                            <div class="alert alert-success">
                                <i class="fas fa-bullseye me-2"></i>
                                <strong>Most Common Root Cause:</strong> {{ analysis.most_common_cause[0]|title|replace('_', ' ') }}
                                ({{ analysis.most_common_cause[1] }} occurrences)
                            </div>
                            {% endif %}

                            <h6 class="fw-bold mb-3">Actionable Recommendations</h6>
                            {% for rec in analysis.recommendations %}
                                <div class="recommendation-card">
                                <!-- Error details for this recommendation (always show, fallback if missing) -->
                                <div class="mb-2">
                                    <span class="badge bg-danger me-2"><i class="fas fa-bug me-1"></i>Error Details:</span>
                                    <span class="fw-bold text-dark">
                                        {% if rec.error_message %}
                                            {{ rec.error_message }}
                                        {% elif rec.original_error %}
                                            {{ rec.original_error }}
                                        {% elif rec.message %}
                                            {{ rec.message }}
                                        {% elif rec.extracted_error %}
                                            {{ rec.extracted_error }}
                                        {% elif rec.log_line %}
                                            {{ rec.log_line }}
                                        {% elif rec.error %}
                                            {{ rec.error }}
                                        {% elif rec.description %}
                                            {{ rec.description }}
                                        {% elif rec.problem %}
                                            {{ rec.problem }}
                                        {% else %}
                                            No specific error found for this recommendation
                                        {% endif %}
                                    </span>
                                    {% if rec.error_line_number is defined and rec.error_line_number %}
                                        <span class="badge bg-secondary ms-2">Line {{ rec.error_line_number }}</span>
                                    {% endif %}
                                    {% if rec.error_category is defined and rec.error_category %}
                                        <span class="badge bg-primary ms-2">{{ rec.error_category|title }}</span>
                                    {% endif %}
                                    {% if rec.error_timestamp is defined and rec.error_timestamp %}
                                        <span class="badge bg-info ms-2">{{ rec.error_timestamp }}</span>
                                    {% endif %}
                                    {% if rec.error_level is defined and rec.error_level %}
                                        <span class="badge bg-dark ms-2">{{ rec.error_level|capitalize }}</span>
                                    {% endif %}
                                </div>
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="fw-bold mb-0">{{ rec.problem if rec.problem is defined else rec.cause|title|replace('_', ' ') }}</h6>
                                    <div class="d-flex gap-2">
                                        {% if rec.source is defined %}
                                        <span class="badge bg-info">
                                            {% if rec.source == 'knowledge_base' %}
                                            <i class="fas fa-book me-1"></i>Knowledge Base
                                            {% elif rec.source == 'ai_generated' %}
                                            <i class="fas fa-robot me-1"></i>AI Generated
                                            {% elif rec.source == 'rag_enhanced' %}
                                            <i class="fas fa-brain me-1"></i>RAG Enhanced
                                            {% else %}
                                            <i class="fas fa-info-circle me-1"></i>{{ rec.source|title }}
                                            {% endif %}
                                        </span>
                                        {% endif %}
                                        {% if rec.confidence is defined %}
                                        <span class="badge bg-{{ 'success' if rec.confidence == 'HIGH' else 'warning' if rec.confidence == 'MEDIUM' else 'secondary' }}">
                                            <i class="fas fa-chart-line me-1"></i>{{ rec.confidence }} Confidence
                                        </span>
                                        {% endif %}
                                        {% if rec.historical_success_rate is defined %}
                                        <span class="badge bg-primary"></span>
                                            <i class="fas fa-trophy me-1"></i>{{ (rec.historical_success_rate * 100)|round }}% Success Rate
                                        </span>
                                        {% endif %}
                                        {% if rec.severity is defined %}
                                        <span class="priority-badge priority-{{ rec.severity|lower }}">
                                            {{ rec.severity|capitalize }}
                                        </span>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Display the main description from LLM-generated recommendations -->
                                {% if rec.description is defined and rec.description %}
                                <div class="mb-3">
                                    <div class="alert alert-light border-start border-4 border-primary">
                                        <p class="mb-0">{{ rec.description|safe }}</p>
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Display steps if available -->
                                {% if rec.steps is defined and rec.steps %}
                                <div class="mb-3">
                                    <strong>Action Steps:</strong>
                                    <ul class="list-unstyled mt-2">
                                        {% for step in rec.steps %}
                                        <li>• {{ step|safe }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}

                                <!-- Display reasoning if available -->
                                {% if rec.reasoning is defined and rec.reasoning %}
                                <div class="mb-3">
                                    <div class="alert alert-info">
                                        <small><strong>Why this matters:</strong> {{ rec.reasoning|safe }}</small>
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Display related errors if available -->
                                {% if rec.related_errors is defined and rec.related_errors %}
                                <div class="mb-3">
                                    <h6 class="fw-bold text-muted mb-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Related Errors:
                                    </h6>
                                    <div class="related-errors">
                                        {% for error in rec.related_errors %}
                                        <div class="alert alert-warning border-start border-4 border-warning mb-2">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <small class="text-muted fw-bold">{{ error.category|upper }}</small>
                                                    <div class="mt-1">
                                                        <code class="text-dark bg-light p-1 rounded">{{ error.message }}</code>
                                                    </div>
                                                </div>
                                                <span class="badge bg-{{ 'danger' if error.severity == 'critical' else 'warning' if error.severity == 'high' else 'info' }} ms-2">
                                                    {{ error.severity|capitalize }}
                                                </span>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if rec.solutions is defined and rec.solutions %}
                                <div class="mb-3">
                                    <strong>Solutions:</strong>
                                    <ul class="list-unstyled mt-2">
                                        {% for solution in rec.solutions %}
                                        <li>• {{ solution|safe }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}

                                {% if rec.root_causes is defined and rec.root_causes %}
                                <div class="mb-3">
                                    <strong>Root Causes:</strong>
                                    <ul class="list-unstyled mt-2">
                                        {% for cause in rec.root_causes %}
                                        <li>• {{ cause }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}

                                {% if rec.prevention is defined and rec.prevention %}
                                <div class="mb-3">
                                    <strong>Prevention:</strong>
                                    <ul class="list-unstyled mt-2">
                                        {% for prevention in rec.prevention %}
                                        <li>• {{ prevention }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}

                                {% if rec.implementation_notes is defined and rec.implementation_notes %}
                                <div class="mb-3">
                                    <strong>Implementation Notes:</strong>
                                    <ul class="list-unstyled mt-2">
                                        {% for note in rec.implementation_notes %}
                                        <li>• {{ note }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}

                                {% if rec.tools is defined and rec.tools %}
                                <div class="mb-3">
                                    <strong>Recommended Tools:</strong>
                                    <ul class="list-inline mt-2">
                                        {% for tool in rec.tools %}
                                        <li class="list-inline-item badge bg-secondary">{{ tool }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}

                                {% if rec.estimated_time is defined %}
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>Estimated Time: {{ rec.estimated_time }}
                                    </small>
                                </div>
                                {% endif %}

                                {% if rec.difficulty_level is defined %}
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-chart-bar me-1"></i>Difficulty: {{ rec.difficulty_level|title }}
                                    </small>
                                </div>
                                {% endif %}

                                <!-- Display knowledge base match if available (RAG enhancement) -->
                                {% if rec.knowledge_base_match is defined and rec.knowledge_base_match %}
                                <div class="mb-3">
                                    <h6 class="fw-bold text-muted mb-2">
                                        <i class="fas fa-database me-1"></i>Based on Historical Solution:
                                    </h6>
                                    <div class="alert alert-info border-start border-4 border-info">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <strong>{{ rec.knowledge_base_match.title }}</strong>
                                                <p class="mb-1 mt-1">{{ rec.knowledge_base_match.problem }}</p>
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>Resolution Time: {{ rec.knowledge_base_match.resolution_time }}
                                                    <span class="ms-2">
                                                        <i class="fas fa-cogs me-1"></i>Technologies: {{ rec.knowledge_base_match.technologies|join(', ') }}
                                                    </span>
                                                </small>
                                            </div>
                                            <span class="badge bg-success">
                                                {{ (rec.knowledge_base_match.success_rate * 100)|round }}% Success
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                        
                        <div class="col-md-4">
                            <h6 class="fw-bold mb-3">Technology Patterns</h6>
                            {% if analysis.patterns and analysis.patterns.technology_detected %}
                            {% for tech in analysis.patterns.technology_detected %}
                            <div class="mb-2">
                                <span class="badge bg-primary">{{ tech|title }}</span>
                            </div>
                            {% endfor %}
                            {% else %}
                            <div class="alert alert-info">
                                <small>No technology patterns detected.</small>
                            </div>
                            {% endif %}

                            <h6 class="fw-bold mb-3 mt-4">Common Issues</h6>
                            {% if analysis.patterns and analysis.patterns.common_issues %}
                            {% for issue in analysis.patterns.common_issues %}
                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <span class="small fw-bold">{{ issue|title|replace('_', ' ') }}</span>
                                    <span class="badge bg-warning">Detected</span>
                                </div>
                            </div>
                            {% endfor %}
                            {% elif result.root_cause_analysis.cause_frequency %}
                            <!-- Fallback to old structure -->
                            {% for cause, count in result.root_cause_analysis.cause_frequency.items() %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="small fw-bold">{{ cause|title|replace('_', ' ') }}</span>
                                    <span class="small">{{ count }}</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ (count / result.classified_errors.total_errors * 100)|round(1) }}%"></div>
                                </div>
                            </div>
                            {% endfor %}
                            {% else %}
                            <div class="alert alert-info">
                                <small>No common issues detected.</small>
                            </div>
                            {% endif %}

                            <h6 class="fw-bold mb-3 mt-4">Analysis Details</h6>
                            <div class="mb-2">
                                <span class="small fw-bold">Analysis Duration:</span>
                                <span class="small">{{ analysis.analysis_duration_ms }}ms</span>
                            </div>
                            <div class="mb-2">
                                <span class="small fw-bold">Log Lines:</span>
                                <span class="small">{{ analysis.log_count }}</span>
                            </div>
                            <div class="mb-2">
                                <span class="small fw-bold">Severity:</span>
                                <span class="badge bg-{{ 'danger' if analysis.severity == 'critical' else 'warning' if analysis.severity == 'high' else 'info' }}">
                                    {{ analysis.severity|title }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Error Classification Tab -->
                <div class="tab-pane fade" id="errors" role="tabpanel">
                    <h5 class="fw-bold mb-3">Error Classification Results</h5>
                    
                    {% if analysis.extracted_errors %}
                    <!-- Group errors by category for the new structure (use all extracted_errors) -->
                    {% set error_categories = {} %}
                    {% for error in analysis.extracted_errors %}
                        {% set category = error.category %}
                        {% if category not in error_categories %}
                            {% set _ = error_categories.update({category: []}) %}
                        {% endif %}
                        {% set _ = error_categories[category].append(error) %}
                    {% endfor %}

                    {% for category, errors in error_categories.items() %}
                    <div class="error-category">
                        <h6 class="fw-bold">
                            <i class="fas fa-tag me-2"></i>{{ category|title|replace('_', ' ') }}
                            <span class="badge bg-primary ms-2">{{ errors|length }}</span>
                        </h6>
                        <div class="error-list">
                            {% for error in errors %}
                            <div class="mb-2 p-2 bg-white rounded border">
                                <small class="text-muted">{{ error.message }}</small>
                                {% if error.frequency %}
                                <span class="badge bg-secondary ms-2">{{ error.frequency }}x</span>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}

                    {% elif result.classified_errors.categories %}
                    <!-- Fallback to old structure -->
                    {% for category, errors in result.classified_errors.categories.items() %}
                    {% if errors %}
                    <div class="error-category">
                        <h6 class="fw-bold">
                            <i class="fas fa-tag me-2"></i>{{ category|title|replace('_', ' ') }}
                            <span class="badge bg-primary ms-2">{{ errors|length }}</span>
                        </h6>
                        <div class="error-list">
                            {% for error in errors[:5] %}
                            <div class="mb-2 p-2 bg-white rounded border">
                                <small class="text-muted">{{ error }}</small>
                            </div>
                            {% endfor %}
                            {% if errors|length > 5 %}
                            <div class="text-center">
                                <small class="text-muted">
                                    <i class="fas fa-ellipsis-h me-1"></i>
                                    and {{ errors|length - 5 }} more errors
                                </small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                    
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No errors found in the analyzed log data.
                    </div>
                    {% endif %}
                </div>

                <!-- Anomaly Detection Tab -->
                <div class="tab-pane fade" id="anomalies" role="tabpanel">
                    <h5 class="fw-bold mb-3">Anomaly Detection Results</h5>
                    
                    {% if analysis.top_errors %}
                        {% for error in analysis.top_errors %}
                        <div class="anomaly-item">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="fw-bold mb-0">{{ error.category|title|replace('_', ' ') }} Error</h6>
                                <span class="priority-badge priority-{{ error.severity.lower() }}">
                                    {{ error.severity.upper() }}
                                </span>
                            </div>
                            <p class="mb-2">{{ error.message }}</p>
                            
                            {% if error.context %}
                                <div class="anomaly-details mt-3">
                                    <div class="alert alert-dark">
                                        <h6 class="fw-bold mb-2">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            Error Context
                                        </h6>
                                        <pre class="stack-trace-block mb-0"><code>{{ error.context }}</code></pre>
                                    </div>
                                </div>
                            {% endif %}
                            
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>Line {{ error.line_number }} 
                                {% if error.frequency %}
                                | <i class="fas fa-repeat me-1"></i>{{ error.frequency }} occurrences
                                {% endif %}
                            </small>
                        </div>
                        {% endfor %}
                    {% elif result.anomalies.anomalies_detected %}
                        {% for anomaly in result.anomalies.anomalies_detected %}
                        <div class="anomaly-item">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="fw-bold mb-0">Anomaly Detected</h6>
                                <span class="priority-badge priority-{{ anomaly.severity.lower() }}">
                                    {{ anomaly.severity.upper() }}
                                </span>
                            </div>
                            <p class="mb-2">{{ anomaly.message }}</p>
                            
                            {% if anomaly.details %}
                                <div class="anomaly-details mt-3">
                                    {% if anomaly.details.type == 'multiline_stack_trace' %}
                                        <div class="alert alert-dark">
                                            <h6 class="fw-bold mb-2">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                Full Stack Trace ({{ anomaly.details.line_count }} lines)
                                            </h6>
                                            {% if anomaly.details.full_trace %}
                                                <pre class="stack-trace-block mb-0"><code>{{ anomaly.details.full_trace }}</code></pre>
                                            {% endif %}
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Starting at line {{ anomaly.details.starting_line }}
                                                </small>
                                            </div>
                                        </div>
                                    {% elif anomaly.details.type == 'critical_pattern' %}
                                        <div class="alert alert-danger">
                                            <h6 class="fw-bold mb-2">
                                                <i class="fas fa-exclamation-circle me-2"></i>
                                                Critical Pattern Details
                                            </h6>
                                            <p class="mb-1"><strong>Pattern:</strong> {{ anomaly.details.pattern }}</p>
                                            <p class="mb-0"><strong>Content:</strong> {{ anomaly.details.line_content }}</p>
                                        </div>
                                    {% elif anomaly.details.type == 'error_frequency' %}
                                        <div class="alert alert-warning">
                                            <h6 class="fw-bold mb-2">
                                                <i class="fas fa-chart-line me-2"></i>
                                                Error Frequency Details
                                            </h6>
                                            <p class="mb-1"><strong>Category:</strong> {{ anomaly.details.category.replace('_', ' ').title() }}</p>
                                            <p class="mb-0"><strong>Count:</strong> {{ anomaly.details.count }} occurrences</p>
                                        </div>
                                    {% endif %}
                                </div>
                            {% endif %}
                            
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>{{ anomaly.timestamp }}
                            </small>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No anomalies detected in the analyzed log data.
                        </div>
                    {% endif %}
                </div>

                <!-- Visual Analysis Tab -->
                <div class="tab-pane fade" id="charts" role="tabpanel">
                    <h5 class="fw-bold mb-3">Visual Analysis</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <canvas id="errorCategoryChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <canvas id="rootCauseChart"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <canvas id="priorityScoreChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <canvas id="severityChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- All Extracted Errors Tab -->
                <div class="tab-pane fade" id="all-errors" role="tabpanel">
                    {% if analysis.extracted_errors is defined and analysis.extracted_errors %}
                    <div class="mt-4">
                        <h5 class="fw-bold mb-3"><i class="fas fa-list me-2"></i>All Extracted Errors</h5>
                        <ul class="list-group">
                            {% for err in analysis.extracted_errors %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="badge bg-secondary me-2">{{ err.category|upper }}</span>
                                    <span class="badge bg-light text-dark me-2">{{ err.level|capitalize }}</span>
                                    <span class="text-monospace">{{ err.message }}</span>
                                </div>
                                {% if err.timestamp %}
                                <span class="badge bg-info text-dark">{{ err.timestamp }}</span>
                                {% endif %}
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% else %}
                    <div class="alert alert-info mt-4">
                        <i class="fas fa-info-circle me-2"></i>No errors extracted from the log.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Prepare data for charts - adapt to new structure
        let errorCategories = [];
        let errorCounts = [];
        let rootCauseLabels = [];
        let rootCauseCounts = [];
        let priorityLabels = [];
        let priorityScores = [];
        let severityData = {};

        // Always use extracted_errors for charts (1:1 mapping)
        {% if analysis.extracted_errors %}
        // Group errors by category for charts
        const errorGroups = {};
        const severityGroups = {};
        {% for error in analysis.extracted_errors %}
        const category = "{{ error.category }}";
        const severity = "{{ error.severity|upper }}";
        if (!errorGroups[category]) {
            errorGroups[category] = 0;
        }
        errorGroups[category]++;
        if (!severityGroups[severity]) {
            severityGroups[severity] = 0;
        }
        severityGroups[severity]++;
        {% endfor %}

        errorCategories = Object.keys(errorGroups);
        errorCounts = Object.values(errorGroups);

        // Root cause chart: use cause_frequency if available
        {% if analysis.cause_frequency %}
        rootCauseLabels = {{ analysis.cause_frequency.keys()|list|tojson }};
        rootCauseCounts = {{ analysis.cause_frequency.values()|list|tojson }};
        {% else %}
        rootCauseLabels = errorCategories;
        rootCauseCounts = errorCounts;
        {% endif %}

        // Priority chart: use priority_scores if available
        {% if analysis.priority_scores %}
        priorityLabels = {{ analysis.priority_scores.keys()|list|tojson }};
        priorityScores = {{ analysis.priority_scores.values()|list|tojson }};
        {% else %}
        priorityLabels = errorCategories;
        priorityScores = errorCounts;
        {% endif %}

        // Severity chart
        severityData = severityGroups;
        {% elif result.classified_errors.categories %}
        // Fallback to old structure
        errorCategories = {{ result.classified_errors.categories.keys()|list|tojson }};
        errorCounts = {{ result.classified_errors.categories.values()|map('length')|list|tojson }};
        rootCauseLabels = {{ result.root_cause_analysis.cause_frequency.keys()|list|tojson }};
        rootCauseCounts = {{ result.root_cause_analysis.cause_frequency.values()|list|tojson }};
        priorityLabels = {{ result.root_cause_analysis.priority_scores.keys()|list|tojson }};
        priorityScores = {{ result.root_cause_analysis.priority_scores.values()|list|tojson }};
        severityData = {{ result.classified_errors.severity_scores or {} }};
        {% endif %}

        // Color schemes
        const colorScheme = [
            '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#F97316', '#06B6D4', '#84CC16'
        ];

        // Error Category Chart
        const errorCategoryCtx = document.getElementById('errorCategoryChart').getContext('2d');
        if (errorCategories.length > 0) {
            new Chart(errorCategoryCtx, {
                type: 'doughnut',
                data: {
                    labels: errorCategories.map(cat => cat.replace('_', ' ').toUpperCase()),
                    datasets: [{
                        data: errorCounts,
                        backgroundColor: colorScheme.slice(0, errorCategories.length),
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Error Categories Distribution'
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        } else {
            // Show test chart with static data if no data available
            new Chart(errorCategoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Database', 'Network', 'Application'],
                    datasets: [{
                        data: [3, 2, 4],
                        backgroundColor: ['#3B82F6', '#EF4444', '#10B981'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Error Categories Distribution (Test Data)'
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Root Cause Chart
        const rootCauseCtx = document.getElementById('rootCauseChart').getContext('2d');
        if (rootCauseLabels.length > 0) {
            new Chart(rootCauseCtx, {
                type: 'bar',
                data: {
                    labels: rootCauseLabels.map(cause => cause.replace('_', ' ').toUpperCase()),
                    datasets: [{
                        label: 'Frequency',
                        data: rootCauseCounts,
                        backgroundColor: colorScheme[0],
                        borderColor: colorScheme[0],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Root Cause Frequency'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        } else {
            // Show message when no data
            rootCauseCtx.fillText('No root cause data found', 150, 150);
        }

        // Priority Score Chart
        const priorityScoreCtx = document.getElementById('priorityScoreChart').getContext('2d');
        if (priorityLabels.length > 0) {
            new Chart(priorityScoreCtx, {
                type: 'radar',
                data: {
                    labels: priorityLabels.map(label => label.replace('_', ' ').toUpperCase()),
                    datasets: [{
                        label: 'Priority Score',
                        data: priorityScores,
                        borderColor: colorScheme[2],
                        backgroundColor: colorScheme[2] + '20',
                        pointBackgroundColor: colorScheme[2],
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: colorScheme[2]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Priority Scores'
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        } else {
            // Show message when no data
            priorityScoreCtx.fillText('No priority data found', 150, 150);
        }

        // Severity Chart
        const severityLabels = Object.keys(severityData);
        const severityValues = Object.values(severityData);
        const severityCtx = document.getElementById('severityChart').getContext('2d');
        if (severityLabels.length > 0) {
            new Chart(severityCtx, {
                type: 'pie',
                data: {
                    labels: severityLabels.map(label => label.toUpperCase()),
                    datasets: [{
                        data: severityValues,
                        backgroundColor: [colorScheme[1], colorScheme[3], colorScheme[2]],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Severity Distribution'
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        } else {
            // Show message when no data
            severityCtx.fillText('No severity data found', 150, 150);
        }
    </script>
</body>
</html>
