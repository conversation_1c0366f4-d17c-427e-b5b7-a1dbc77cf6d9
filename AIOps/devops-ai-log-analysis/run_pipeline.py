#!/usr/bin/env python3
"""
Simple run script for testing the DevOps AI Log Analysis application.
This bypasses terminal issues by running Python directly.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    try:
        # Import modules
        from ingestion.ingest_text import ingest_text_logs
        from ingestion.ingest_xml import ingest_xml_logs
        from ingestion.ingest_json import ingest_json_logs
        from ingestion.ingest_timeseries import ingest_timeseries_logs
        from preprocessing.preprocess_text import preprocess_text
        from preprocessing.preprocess_xml import preprocess_xml
        from preprocessing.preprocess_json import preprocess_json
        from preprocessing.preprocess_timeseries import preprocess_timeseries
        from error_classification.classify_errors import classify_errors
        from root_cause_analysis.analyze_root_cause import analyze_root_cause
        from anomaly_detection.detect_anomalies import detect_anomalies
        from trend_detection.detect_trends import detect_trends

        print("DevOps AI Log Analysis - Running Pipeline")
        print("=" * 50)

        # Ingest logs from various formats
        print("1. Ingesting logs...")
        text_logs = ingest_text_logs('data/raw/text_logs.txt')
        xml_logs = ingest_xml_logs('data/raw/logs.xml')
        json_logs = ingest_json_logs('data/raw/logs.json')
        timeseries_logs = ingest_timeseries_logs('data/raw/timeseries_logs.csv')

        print(f"   - Text logs: {len(text_logs)} entries")
        print(f"   - XML logs: {len(xml_logs)} entries")
        print(f"   - JSON logs: {len(json_logs)} entries")
        print(f"   - Timeseries logs: {len(timeseries_logs) if timeseries_logs is not None else 0} entries")

        # Preprocess the ingested logs
        print("\n2. Preprocessing logs...")
        preprocessed_text = preprocess_text(text_logs)
        preprocessed_xml = preprocess_xml(xml_logs)
        preprocessed_json = preprocess_json(json_logs)
        preprocessed_timeseries = preprocess_timeseries(timeseries_logs)

        print(f"   - Preprocessed text: {len(preprocessed_text)} entries")
        print(f"   - Preprocessed XML: {len(preprocessed_xml)} entries")
        print(f"   - Preprocessed JSON: {len(preprocessed_json)} entries")
        print(f"   - Preprocessed timeseries: {len(preprocessed_timeseries) if preprocessed_timeseries is not None else 0} entries")

        # Classify errors in the logs
        print("\n3. Classifying errors...")
        classified_errors = classify_errors(preprocessed_text)
        print(f"   - Total errors classified: {classified_errors['total_errors']}")
        print(f"   - Categories: {list(classified_errors['categories'].keys())}")

        # Perform root cause analysis
        print("\n4. Analyzing root causes...")
        root_causes = analyze_root_cause(classified_errors)
        print(f"   - {root_causes['summary']}")

        # Detect anomalies in the logs
        print("\n5. Detecting anomalies...")
        anomalies = detect_anomalies(preprocessed_json)
        print(f"   - {anomalies['summary']}")

        # Detect trends in the time series data
        print("\n6. Detecting trends...")
        trends = detect_trends(preprocessed_timeseries)
        print(f"   - {trends['summary']}")

        # Output detailed results
        print("\n" + "=" * 50)
        print("DETAILED RESULTS:")
        print("=" * 50)
        
        print("\nClassified Errors:")
        for category, errors in classified_errors['categories'].items():
            if errors:
                print(f"  {category}: {len(errors)} errors")
                for error in errors[:2]:  # Show first 2 errors
                    print(f"    - {error}")
                if len(errors) > 2:
                    print(f"    ... and {len(errors) - 2} more")

        print("\nRoot Causes:")
        for category, causes in root_causes['root_causes'].items():
            if causes:
                print(f"  {category}: {set(causes)}")

        print("\nAnomalies:")
        if anomalies['anomalies']:
            for anomaly in anomalies['anomalies'][:3]:  # Show first 3
                print(f"  - {anomaly['type']}: {anomaly['data'].get('message', 'N/A')}")

        print("\nTrends:")
        for metric, trend_data in trends['trends'].items():
            print(f"  {metric}: {trend_data['direction']} (slope: {trend_data['slope']:.3f})")

        print("\n" + "=" * 50)
        print("✓ Pipeline completed successfully!")

    except Exception as e:
        print(f"✗ Error running pipeline: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
