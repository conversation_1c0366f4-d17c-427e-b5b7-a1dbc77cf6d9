# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# mypy
.mypy_cache/
.dmypy.json
.dmypy.json.*/

# VS Code
.vscode/

# Virtual environments
.venv/
venv/
ENV/
env.bak/
venv.bak/

# MacOS
.DS_Store

# Logs
*.log

# Ignore uploads and temp data
uploads/
ui/uploads/

# Ignore demo/test artifacts
mock_ai_analysis.json
demo_results.json
demo_root_cause_analysis.py
test_demo_data.py
test_precision.py
test_errors.log
test_precision_logs.txt
sample_queries.json

# Ignore knowledge base backups
knowledge_base_backup.json

# Ignore other editor folders
.idea/

# Ignore Python egg files
*.egg
*.egg-info/

# Ignore build artifacts
*.o
*.a
*.so
*.out
*.exe

# Ignore coverage reports
.coverage

# Ignore temporary files
*.tmp
*.swp
*.swo

# Ignore node_modules (if any JS tooling is used)
node_modules/
