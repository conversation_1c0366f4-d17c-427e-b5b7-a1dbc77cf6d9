# Core data processing
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Natural language processing
nltk>=3.8
spacy>=3.7.0

# Data formats
xmltodict>=0.13.0
jsonschema>=4.17.0

# Statistical analysis
statsmodels>=0.14.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0

# Machine learning and deep learning (Python 3.13 compatible)
tensorflow>=2.15.0
keras>=2.15.0
torch>=2.0.0
torchvision>=0.15.0

# Anomaly detection
pyod>=1.1.0

# Utilities
requests>=2.31.0
psutil>=5.9.0
tqdm>=4.65.0

# Advanced ML/NLP (optional - will install if compatible)
transformers>=4.30.0
datasets>=2.12.0
accelerate>=0.20.0

# Web UI dependencies
flask>=2.3.0
werkzeug>=2.3.0
jinja2>=3.1.0
markupsafe>=2.1.0

# Additional utilities for UI
python-dateutil>=2.8.0
six>=1.16.0