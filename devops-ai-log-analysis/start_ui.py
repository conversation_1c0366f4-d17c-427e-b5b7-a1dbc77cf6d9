#!/usr/bin/env python3
"""
Startup script for the DevOps AI Log Analysis Web UI
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_virtual_environment():
    """Check if we're in a virtual environment"""
    return hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    
    # Try full requirements first
    try:
        print("Attempting to install full requirements...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Full requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Full requirements failed: {e}")
        
        # Fallback to minimal requirements
        if os.path.exists("requirements-minimal.txt"):
            print("Trying minimal requirements for UI functionality...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements-minimal.txt"])
                print("✓ Minimal requirements installed successfully")
                print("ℹ️  Note: Some advanced ML features may not be available")
                return True
            except subprocess.CalledProcessError as e2:
                print(f"✗ Error installing minimal requirements: {e2}")
                return False
        else:
            print("✗ No fallback requirements file found")
            return False

def create_directories():
    """Create necessary directories"""
    dirs_to_create = [
        "ui/uploads",
        "ui/static",
        "data/processed",
        "data/raw",
        "models"
    ]
    
    for dir_path in dirs_to_create:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {dir_path}")

def start_ui_server():
    """Start the Flask UI server"""
    print("\n" + "="*60)
    print("🚀 Starting DevOps AI Log Analysis Web UI")
    print("="*60)
    print("✓ Server starting on http://localhost:5000")
    print("✓ Press Ctrl+C to stop the server")
    print("="*60)
    
    try:
        os.chdir("ui")
        subprocess.run([sys.executable, "app.py"], check=True)
    except KeyboardInterrupt:
        print("\n\n✓ Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"\n✗ Error starting server: {e}")
    except FileNotFoundError:
        print("\n✗ Error: ui/app.py not found. Please make sure you're in the project root directory.")

def main():
    print("DevOps AI Log Analysis - Web UI Startup")
    print("="*50)
    
    # Check if we're in the right directory
    if not os.path.exists("requirements.txt"):
        print("✗ Error: requirements.txt not found. Please run this script from the project root directory.")
        sys.exit(1)
    
    # Check virtual environment
    if not check_virtual_environment():
        print("⚠️  Warning: Not running in a virtual environment.")
        print("   It's recommended to use a virtual environment.")
        response = input("   Continue anyway? (y/n): ")
        if response.lower() != 'y':
            print("   Please activate your virtual environment and try again.")
            sys.exit(1)
    else:
        print("✓ Running in virtual environment")
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        print("✗ Failed to install requirements. Please check your Python environment.")
        sys.exit(1)
    
    # Wait a moment for installations to complete
    time.sleep(2)
    
    # Start the UI server
    start_ui_server()

if __name__ == "__main__":
    main()
