<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevOps AI Log Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #f8fafc;
            --accent-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
        }
        
        body {
            background-color: var(--secondary-color);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
            color: white;
            padding: 4rem 0;
            margin-bottom: 2rem;
        }
        
        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s, box-shadow 0.2s;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        .feature-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--accent-color), #059669);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #1e40af, var(--primary-color));
            transform: translateY(-1px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, var(--accent-color), #059669);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .footer {
            background: #1f2937;
            color: white;
            padding: 2rem 0;
            margin-top: 4rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-robot me-2"></i>AIOps
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link" href="/upload">Upload Logs</a>
                <a class="nav-link" href="/chat">Ask Assistant</a>
                <a class="nav-link" href="/demo">Demo</a>
            </div>
        </div>
    </nav>

    <div class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">
                        AI-Powered DevOps Assistant
                    </h1>
                    <p class="lead mb-4">
                        Automatically analyze log files, detect anomalies, classify errors, and identify root causes with advanced machine learning and AI techniques.
                    </p>
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="/upload" class="btn btn-light btn-lg">
                            <i class="fas fa-upload me-2"></i>Upload Logs
                        </a>
                        <a href="/chat" class="btn btn-success btn-lg">
                            <i class="fas fa-robot me-2"></i>Ask Assistant
                        </a>
                        <a href="/demo" class="btn btn-outline-light btn-lg" id="demoBtn">
                            <i class="fas fa-play me-2"></i>Try Demo
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stats-number">99.9%</div>
                                <div class="text-muted">Accuracy</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stats-number">10x</div>
                                <div class="text-muted">Faster Analysis</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stats-number">24/7</div>
                                <div class="text-muted">Monitoring</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stats-number">AI</div>
                                <div class="text-muted">Powered</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row mb-5">
            <div class="col-lg-12 text-center">
                <h2 class="display-6 fw-bold mb-4">Advanced Log Analysis Features</h2>
                <p class="lead text-muted mb-5">
                    Comprehensive suite of AI-powered tools for modern DevOps log analysis
                </p>
            </div>
        </div>

        <div class="row g-4 mb-5">
            <div class="col-md-6 col-lg-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-search-plus text-white fa-2x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Anomaly Detection</h5>
                    <p class="text-muted">
                        Advanced machine learning algorithms detect unusual patterns and anomalies in your log data in real-time.
                    </p>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-tags text-white fa-2x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Error Classification</h5>
                    <p class="text-muted">
                        Automatically categorize errors by type, severity, and source using intelligent pattern recognition.
                    </p>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bullseye text-white fa-2x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Root Cause Analysis</h5>
                    <p class="text-muted">
                        Identify the underlying causes of issues with AI-powered analysis and actionable recommendations.
                    </p>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line text-white fa-2x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Trend Analysis</h5>
                    <p class="text-muted">
                        Visualize error trends over time and predict potential issues before they impact your system.
                    </p>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-file-alt text-white fa-2x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Multi-Format Support</h5>
                    <p class="text-muted">
                        Process logs in various formats including JSON, XML, CSV, and plain text with multiline support.
                    </p>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-robot text-white fa-2x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">AI Assistant</h5>
                    <p class="text-muted">
                        Ask natural language questions about your logs and get intelligent responses with actionable insights.
                    </p>
                </div>
            </div>
            <div class="col-md-6 col-lg-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-lightning-bolt text-white fa-2x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Real-Time Processing</h5>
                    <p class="text-muted">
                        Process large log files efficiently with streaming capabilities and real-time analysis.
                    </p>
                </div>
            </div>
        </div>

        <div class="row mb-5">
            <div class="col-lg-12 text-center">
                <h3 class="fw-bold mb-4">Get Started in 3 Easy Steps</h3>
                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="feature-icon mx-auto mb-3">
                                <i class="fas fa-upload text-white fa-2x"></i>
                            </div>
                            <h5 class="fw-bold">1. Upload Your Logs</h5>
                            <p class="text-muted">Simply drag and drop your log files or browse to select them.</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="feature-icon mx-auto mb-3">
                                <i class="fas fa-cog text-white fa-2x"></i>
                            </div>
                            <h5 class="fw-bold">2. AI Analysis</h5>
                            <p class="text-muted">Our AI engine automatically processes and analyzes your logs.</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="feature-icon mx-auto mb-3">
                                <i class="fas fa-chart-bar text-white fa-2x"></i>
                            </div>
                            <h5 class="fw-bold">3. View Results</h5>
                            <p class="text-muted">Get detailed insights, recommendations, and actionable reports.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12 text-center">
                <div class="bg-primary bg-gradient text-white p-5 rounded-3">
                    <h3 class="fw-bold mb-3">Ready to Analyze Your Logs?</h3>
                    <p class="lead mb-4">
                        Start analyzing your log files with our advanced AI-powered system.
                    </p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="/upload" class="btn btn-light btn-lg">
                            <i class="fas fa-upload me-2"></i>Upload Logs Now
                        </a>
                        <a href="/chat" class="btn btn-success btn-lg">
                            <i class="fas fa-robot me-2"></i>Ask Assistant
                        </a>
                        <a href="/demo" class="btn btn-outline-light btn-lg" id="demoBtn2">
                            <i class="fas fa-play me-2"></i>Try Demo First
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-robot me-2"></i>DevOps AI Log Analysis
                    </h5>
                    <p class="text-muted">
                        Advanced AI-powered log analysis and root cause detection for modern DevOps teams.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">
                        Built with <i class="fas fa-heart text-danger"></i> for DevOps professionals
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Enhanced demo loading with progress indicators
        function handleDemoClick(event, buttonId) {
            event.preventDefault();
            
            const btn = document.getElementById(buttonId);
            const originalHtml = btn.innerHTML;
            
            // Show loading state
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading Demo...';
            btn.classList.add('disabled');
            
            // Add visual feedback
            const loadingToast = document.createElement('div');
            loadingToast.className = 'position-fixed top-0 end-0 p-3';
            loadingToast.style.zIndex = '9999';
            loadingToast.innerHTML = `
                <div class="toast show" role="alert">
                    <div class="toast-header bg-primary text-white">
                        <i class="fas fa-rocket me-2"></i>
                        <strong class="me-auto">Demo Loading</strong>
                    </div>
                    <div class="toast-body">
                        Preparing sample log analysis... ⚡
                    </div>
                </div>
            `;
            document.body.appendChild(loadingToast);
            
            // Navigate to demo with small delay for visual feedback
            setTimeout(() => {
                window.location.href = '/demo';
            }, 500);
        }
        
        // Attach event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const demoBtn1 = document.getElementById('demoBtn');
            const demoBtn2 = document.getElementById('demoBtn2');
            
            if (demoBtn1) {
                demoBtn1.addEventListener('click', (e) => handleDemoClick(e, 'demoBtn'));
            }
            
            if (demoBtn2) {
                demoBtn2.addEventListener('click', (e) => handleDemoClick(e, 'demoBtn2'));
            }
        });
        
        // Preload demo data in background for even faster access
        if ('serviceWorker' in navigator) {
            // Prefetch demo data
            fetch('/api/demo/fast', { method: 'GET' })
                .then(() => console.log('✅ Demo data preloaded'))
                .catch(() => console.log('ℹ️ Demo data will be loaded on demand'));
        }
    </script>
</body>
</html>
