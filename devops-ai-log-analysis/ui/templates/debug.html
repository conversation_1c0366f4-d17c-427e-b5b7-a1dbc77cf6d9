<!DOCTYPE html>
<html>
<head>
    <title>Debug Template</title>
    <style>
        .debug-info {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-left: 3px solid #007bff;
        }
        .stack-trace {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Stack Trace Debug Template</h1>
    
    {% if result.anomalies.anomalies_detected %}
        <h2>Found {{ result.anomalies.anomalies_detected|length }} anomalies</h2>
        
        {% for anomaly in result.anomalies.anomalies_detected %}
        <div class="debug-info">
            <h3>Anomaly {{ loop.index }}</h3>
            <p><strong>Message:</strong> {{ anomaly.message }}</p>
            <p><strong>Severity:</strong> {{ anomaly.severity }}</p>
            
            <h4>Details Debug:</h4>
            <p><strong>Has details:</strong> {{ anomaly.details is defined and anomaly.details }}</p>
            
            {% if anomaly.details %}
                <p><strong>Details type:</strong> {{ anomaly.details.type }}</p>
                <p><strong>Has full_trace:</strong> {{ 'full_trace' in anomaly.details }}</p>
                
                {% if anomaly.details.type == 'multiline_stack_trace' %}
                    <h4>✅ Stack Trace Detected!</h4>
                    <p><strong>Line count:</strong> {{ anomaly.details.line_count }}</p>
                    <p><strong>Starting line:</strong> {{ anomaly.details.starting_line }}</p>
                    
                    {% if anomaly.details.full_trace %}
                        <h4>Full Stack Trace:</h4>
                        <div class="stack-trace">{{ anomaly.details.full_trace }}</div>
                    {% else %}
                        <p>❌ No full_trace content found!</p>
                    {% endif %}
                {% else %}
                    <p>Not a stack trace (type: {{ anomaly.details.type }})</p>
                {% endif %}
            {% else %}
                <p>❌ No details found for this anomaly</p>
            {% endif %}
        </div>
        {% endfor %}
    {% else %}
        <h2>❌ No anomalies found</h2>
    {% endif %}
</body>
</html>
