<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Base Management</title>
    <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
    <h1>Knowledge Base Management</h1>

    <h2>Add New Entry</h2>
    <form id="knowledgeBaseForm">
        <label for="pattern">Pattern:</label><br>
        <textarea id="pattern" name="pattern" rows="4" cols="50" required></textarea><br><br>

        <label for="solution">Solution:</label><br>
        <textarea id="solution" name="solution" rows="4" cols="50" required></textarea><br><br>

        <label for="metadata">Metadata (optional):</label><br>
        <textarea id="metadata" name="metadata" rows="2" cols="50"></textarea><br><br>

        <button type="submit">Submit</button>
    </form>

    <h2>Current Knowledge Base</h2>
    <ul id="knowledgeBaseList"></ul>

    <script>
        // Fetch and display the current knowledge base
        async function fetchKnowledgeBase() {
            const response = await fetch('/knowledge_base/api/knowledge_base');
            const data = await response.json();
            const list = document.getElementById('knowledgeBaseList');
            list.innerHTML = '';
            data.forEach(entry => {
                const listItem = document.createElement('li');
                listItem.textContent = `Pattern: ${entry.pattern}, Solution: ${entry.solution}`;
                list.appendChild(listItem);
            });
        }

        // Handle form submission
        document.getElementById('knowledgeBaseForm').addEventListener('submit', async (event) => {
            event.preventDefault();
            const pattern = document.getElementById('pattern').value;
            const solution = document.getElementById('solution').value;
            const metadata = document.getElementById('metadata').value;

            const response = await fetch('/knowledge_base/api/knowledge_base', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ pattern, solution, metadata: metadata ? JSON.parse(metadata) : {} })
            });

            if (response.ok) {
                alert('Entry added successfully!');
                fetchKnowledgeBase();
                document.getElementById('knowledgeBaseForm').reset();
            } else {
                alert('Failed to add entry. Please check your input.');
            }
        });

        // Initial fetch
        fetchKnowledgeBase();
    </script>
</body>
</html>
