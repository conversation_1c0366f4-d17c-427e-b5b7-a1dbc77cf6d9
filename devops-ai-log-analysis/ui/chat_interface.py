"""
Chat Interface for Natural Language Queries
Flask blueprint for handling chat-based log queries.
"""

from flask import Blueprint, render_template, request, jsonify, session
from datetime import datetime
import json
import logging
import os
import sys

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from ai_enhanced.llm_integration import NaturalLanguageQueryProcessor
from ai_enhanced.search_engine import NaturalLanguageSearchEngine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create blueprint
chat_bp = Blueprint('chat', __name__)

# Initialize processors
llm_processor = None
search_engine = None

def initialize_chat_interface():
    """Initialize the chat interface components."""
    global llm_processor, search_engine
    
    try:
        # Initialize LLM processor
        api_key = os.getenv('OPENAI_API_KEY')
        llm_processor = NaturalLanguageQueryProcessor(api_key)
        
        # Initialize search engine
        search_engine = NaturalLanguageSearchEngine()
        
        logger.info("Chat interface initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize chat interface: {e}")
        return False

@chat_bp.route('/')
def chat_interface():
    """Render the chat interface."""
    return render_template('chat.html')

@chat_bp.route('/api/chat', methods=['POST'])
def process_chat():
    """Process natural language queries via chat interface."""
    global llm_processor, search_engine
    
    # Initialize if not already done
    if llm_processor is None or search_engine is None:
        if not initialize_chat_interface():
            return jsonify({'error': 'Chat interface not available'}), 500
    
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({'error': 'No query provided'}), 400
        
        logger.info(f"Processing chat query: {query}")
        
        # Check for cached results first
        cached_results = search_engine.get_cached_results(query)
        if cached_results:
            logger.info("Returning cached results")
            return jsonify({
                'query': query,
                'cached': True,
                'results': cached_results
            })
        
        # Process the query with LLM
        search_params = llm_processor.process_query(query)
        
        # Execute the search
        results = search_engine.execute_search(search_params)
        
        # Format response for chat interface
        response = {
            'query': query,
            'cached': False,
            'success': True,
            'results': results,
            'timestamp': datetime.now().isoformat(),
            'suggestions': llm_processor.get_query_suggestions(query)
        }
        
        logger.info(f"Chat query processed successfully: {results['total_results']} results")
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error processing chat query: {e}")
        return jsonify({
            'error': str(e),
            'success': False,
            'query': query if 'query' in locals() else 'Unknown'
        }), 500

@chat_bp.route('/api/chat/suggestions', methods=['GET'])
def get_suggestions():
    """Get query suggestions for autocomplete."""
    global llm_processor
    
    if llm_processor is None:
        if not initialize_chat_interface():
            return jsonify({'error': 'Chat interface not available'}), 500
    
    try:
        partial_query = request.args.get('q', '')
        suggestions = llm_processor.get_query_suggestions(partial_query)
        
        return jsonify({
            'suggestions': suggestions,
            'partial_query': partial_query
        })
        
    except Exception as e:
        logger.error(f"Error getting suggestions: {e}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/api/chat/history', methods=['GET'])
def get_chat_history():
    """Get chat history for the current session."""
    global search_engine
    
    if search_engine is None:
        return jsonify({'history': []})
    
    try:
        limit = request.args.get('limit', 10, type=int)
        history = search_engine.get_search_history(limit)
        
        return jsonify({
            'history': history,
            'total': len(search_engine.search_history)
        })
        
    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/api/chat/clear', methods=['POST'])
def clear_chat_history():
    """Clear chat history and cache."""
    global search_engine
    
    if search_engine is None:
        return jsonify({'success': False})
    
    try:
        search_engine.search_history.clear()
        search_engine.cached_results.clear()
        
        return jsonify({
            'success': True,
            'message': 'Chat history and cache cleared'
        })
        
    except Exception as e:
        logger.error(f"Error clearing chat history: {e}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/api/chat/status', methods=['GET'])
def chat_status():
    """Get chat interface status and capabilities."""
    global llm_processor, search_engine
    
    status = {
        'llm_available': llm_processor is not None and llm_processor.use_openai,
        'search_engine_available': search_engine is not None,
        'openai_configured': bool(os.getenv('OPENAI_API_KEY')),
        'processing_methods': []
    }
    
    if llm_processor:
        if llm_processor.use_openai:
            status['processing_methods'].append('OpenAI LLM')
        status['processing_methods'].append('Pattern-based')
    
    return jsonify(status)

# Helper function to format search results for chat display
def format_results_for_chat(results: dict) -> dict:
    """Format search results for better display in chat interface."""
    formatted = {
        'summary': {
            'query': results.get('query', ''),
            'total_results': results.get('total_results', 0),
            'confidence': results.get('confidence', 0.0),
            'processing_method': results.get('processing_method', 'pattern'),
            'execution_time': results.get('execution_time_ms', 0)
        },
        'insights': results.get('insights', []),
        'analysis': results.get('analysis', {}),
        'sample_logs': results.get('results', [])[:5],  # Show first 5 results
        'filters_applied': results.get('filters_applied', {}),
        'has_more_results': results.get('total_results', 0) > 5
    }
    
    return formatted

# Example queries for demonstration
EXAMPLE_QUERIES = [
    "Show me all errors in the last hour",
    "Find authentication failures from user-service",
    "What happened in the payment system today?",
    "Show me critical errors in the database",
    "Find all timeouts in the API gateway",
    "Show me failed login attempts in the last 30 minutes",
    "Find all exceptions in the order processing service",
    "What performance issues occurred yesterday?",
    "Show me all security alerts this week",
    "Find database connection errors in the last 24 hours"
]

@chat_bp.route('/api/chat/examples', methods=['GET'])
def get_example_queries():
    """Get example queries for the chat interface."""
    return jsonify({
        'examples': EXAMPLE_QUERIES,
        'total': len(EXAMPLE_QUERIES)
    })

# Initialize on import
initialize_chat_interface()
