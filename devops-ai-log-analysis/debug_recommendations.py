#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from root_cause_analysis.unified_root_cause_analyzer import UnifiedRootCauseAnalyzer

# Test the recommendation generation
analyzer = UnifiedRootCauseAnalyzer()

# Sample log text with errors
test_logs = """
2024-01-01 10:00:00 ERROR [Database] Connection timeout to database server postgres://localhost:5432
2024-01-01 10:01:00 ERROR [Application] Failed to authenticate user with token invalid_token_123
2024-01-01 10:02:00 WARN [System] High memory usage detected: 95% utilized
2024-01-01 10:03:00 ERROR [Jenkins] Build failed: Maven compilation error in module core-service
"""

print("=== TESTING LOG ANALYSIS ===")
results = analyzer.analyze_log_text(test_logs)

print("\n=== EXTRACTED ERRORS ===")
for i, error in enumerate(results.get('extracted_errors', [])):
    print(f"Error {i+1}: {error}")

print(f"\n=== RECOMMENDATIONS ({len(results.get('recommendations', []))}) ===")
for i, rec in enumerate(results.get('recommendations', [])):
    print(f"\nRecommendation {i+1}:")
    print(f"  Problem: {rec.get('problem', 'N/A')}")
    print(f"  Error Message: {rec.get('error_message', 'NOT SET')}")
    print(f"  Error Line Number: {rec.get('error_line_number', 'NOT SET')}")
    print(f"  Error Category: {rec.get('error_category', 'NOT SET')}")
    print(f"  Source: {rec.get('source', 'NOT SET')}")
    print(f"  Description: {rec.get('description', 'N/A')}")
