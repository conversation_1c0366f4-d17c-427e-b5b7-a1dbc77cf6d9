2024-01-15 10:30:15 ERROR: Connection timeout to database server db-prod-01 after 30 seconds
2024-01-15 10:30:16 ERROR: Failed to connect to payment-service on port 8080: Connection refused
2024-01-15 10:30:17 ERROR: Authentication failed <NAME_EMAIL>: Invalid credentials
2024-01-15 10:30:18 ERROR: Out of memory: Java heap space exceeded in order-processing-service
2024-01-15 10:30:19 ERROR: NullPointerException in UserService.validateUser() at line 247
2024-01-15 10:30:20 ERROR: Database connection pool exhausted - max 50 connections reached
2024-01-15 10:30:21 ERROR: SSL certificate expired for api.company.com, valid until 2024-01-10
2024-01-15 10:30:22 ERROR: Network unreachable: timeout connecting to external-api.partner.com
2024-01-15 10:30:23 ERROR: Permission denied accessing /var/log/application.log
2024-01-15 10:30:24 ERROR: Disk full: no space left on device /dev/sda1 (100% used)
2024-01-15 10:30:25 ERROR: Invalid token: JWT token expired for user session abc123
2024-01-15 10:30:26 ERROR: ArrayIndexOutOfBoundsException in DataProcessor.process() index 150 out of bounds
2024-01-15 10:30:27 ERROR: Connection refused by elasticsearch-cluster at 10.0.1.15:9200
2024-01-15 10:30:28 ERROR: Memory allocation failed in background-worker: Cannot allocate 2GB
2024-01-15 10:30:29 ERROR: DNS resolution failed for internal-service.company.local
2024-01-15 10:30:30 ERROR: Transaction rolled back due to deadlock detected in orders table
2024-01-15 10:30:31 ERROR: Rate limit exceeded for API endpoint /api/v1/users (1000 requests/minute)
2024-01-15 10:30:32 ERROR: Configuration file not found: /etc/app/config.yaml
2024-01-15 10:30:33 ERROR: Service unavailable: payment-gateway returned 503 Service Unavailable
2024-01-15 10:30:34 ERROR: Validation failed: invalid email format in user registration
2024-01-15 10:30:35 ERROR: Database query timeout: SELECT * FROM users WHERE active=1 (30s timeout)
2024-01-15 10:30:36 ERROR: Failed to acquire lock on critical section in PaymentProcessor
2024-01-15 10:30:37 ERROR: Unauthorized access attempt from IP *************
2024-01-15 10:30:38 ERROR: Stack overflow in recursive function calculateDiscount()
2024-01-15 10:30:39 ERROR: Connection timeout to Redis cache server after 5 seconds
2024-01-15 10:30:40 ERROR: Invalid API key provided for external service integration
2024-01-15 10:30:41 ERROR: File not found: /app/templates/email_template.html
2024-01-15 10:30:42 ERROR: Division by zero in calculation engine when processing order #12345
2024-01-15 10:30:43 ERROR: Certificate validation failed for HTTPS connection to secure-api.com
2024-01-15 10:30:44 ERROR: Memory leak detected: heap usage increased by 500MB in 10 minutes
2024-01-15 10:30:45 ERROR: Connection pool timeout: no available connections in 30 seconds
2024-01-15 10:30:46 ERROR: Method not found: PaymentService.processRefund() doesn't exist
2024-01-15 10:30:47 ERROR: Assertion failed: expected user.role != null in UserManager.authorize()
2024-01-15 10:30:48 ERROR: Network interface down: eth0 interface is not available
2024-01-15 10:30:49 ERROR: CPU usage high: 95% utilization detected for 5 minutes
2024-01-15 10:30:50 ERROR: Backup failed: insufficient disk space for database backup
2024-01-15 10:31:15 ERROR: Connection timeout to database server db-prod-01 after 30 seconds
2024-01-15 10:31:16 ERROR: Failed to connect to payment-service on port 8080: Connection refused
2024-01-15 10:31:17 ERROR: Authentication failed <NAME_EMAIL>: Invalid credentials
2024-01-15 10:31:18 ERROR: Out of memory: Java heap space exceeded in order-processing-service
2024-01-15 10:31:19 ERROR: Connection timeout to database server db-prod-01 after 30 seconds
2024-01-15 10:31:20 ERROR: SSL certificate expired for api.company.com, valid until 2024-01-10
2024-01-15 10:31:21 ERROR: Network unreachable: timeout connecting to external-api.partner.com
2024-01-15 10:31:22 ERROR: Out of memory: Java heap space exceeded in order-processing-service
2024-01-15 10:31:23 ERROR: Database connection pool exhausted - max 50 connections reached
2024-01-15 10:31:24 ERROR: Connection timeout to database server db-prod-01 after 30 seconds
2024-01-15 10:31:25 ERROR: Authentication failed <NAME_EMAIL>: Invalid credentials
