import pandas as pd
from sklearn.ensemble import IsolationForest, RandomForestClassifier, VotingClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.cluster import DBSCAN, KMeans
from sklearn.metrics import silhouette_score, accuracy_score
from sklearn.decomposition import PCA
from sklearn.svm import OneClassSVM
from sklearn.neural_network import MLPClassifier
from collections import Counter, defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import re
import warnings
warnings.filterwarnings('ignore')

@dataclass
class AnomalyResult:
    index: int
    type: str
    severity: str
    data: Dict[str, Any]
    pattern_matched: Optional[str] = None

class AnomalyDetector:
    def __init__(self, contamination=0.05):
        self.contamination = contamination
        self.model = IsolationForest(contamination=self.contamination)

    def fit(self, data):
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(data)
        self.model.fit(scaled_data)

    def detect_anomalies(self, data):
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(data)
        anomalies = self.model.predict(scaled_data)
        return anomalies

    def evaluate(self, data, true_labels):
        predictions = self.detect_anomalies(data)
        return predictions

class AdvancedAnomalyDetector:
    def __init__(self, contamination=0.05, enable_gpu=False):
        self.contamination = contamination
        self.enable_gpu = enable_gpu
        self.model = IsolationForest(contamination=self.contamination)

    def fit(self, log_data: List[Dict[str, Any]]) -> 'AdvancedAnomalyDetector':
        # Fit the model with log data
        return self

    def detect_anomalies_ensemble(self, log_data: List[Dict[str, Any]]) -> List[AnomalyResult]:
        anomalies = []
        # Ensemble detection logic
        return anomalies

    def detect_time_based_anomalies(self, processed_data: List[Dict[str, Any]]) -> List[AnomalyResult]:
        anomalies = []
        # Time-based detection logic
        return anomalies

    def detect_correlation_anomalies(self, processed_data: List[Dict[str, Any]]) -> List[AnomalyResult]:
        anomalies = []
        # Correlation-based detection logic
        return anomalies

    def deduplicate_anomalies(self, anomalies: List[AnomalyResult]) -> List[AnomalyResult]:
        # Deduplication logic
        return anomalies

    def detect_enhanced_patterns(self, processed_data: List[Dict[str, Any]]) -> List[AnomalyResult]:
        enhanced_patterns = {
            'cascade_failure': {
                'pattern': re.compile(r'(ERROR.*\n){3,}', re.MULTILINE),
                'severity': 'CRITICAL',
                'description': 'Cascade failure pattern detected - multiple consecutive errors'
            },
            'memory_exhaustion': {
                'pattern': re.compile(r'(OutOfMemoryError|MemoryError|heap.*exhausted|OOM|Connection pool exhausted)', re.IGNORECASE),
                'severity': 'CRITICAL',
                'description': 'Memory exhaustion detected'
            },
            'database_timeout': {
                'pattern': re.compile(r'(Database connection timeout|Lock wait timeout|Connection timed out)', re.IGNORECASE),
                'severity': 'HIGH',
                'description': 'Database timeout issues detected'
            },
            'service_failure': {
                'pattern': re.compile(r'(Service unavailable|Service is down|Health check failed)', re.IGNORECASE),
                'severity': 'HIGH',
                'description': 'Service failure detected'
            },
            'authentication_failure': {
                'pattern': re.compile(r'(unauthorized|forbidden|authentication failed|credentials)', re.IGNORECASE),
                'severity': 'MEDIUM',
                'description': 'Authentication failure detected'
            },
            'high_error_rate': {
                'pattern': re.compile(r'(ERROR|FATAL|CRITICAL)', re.IGNORECASE),
                'severity': 'MEDIUM',
                'description': 'High error rate detected'
            }
        }
        
        anomalies = []
        
        # Convert processed_data to text for pattern matching
        if processed_data:
            log_text = '\n'.join([entry.get('message', str(entry)) for entry in processed_data])
            
            # Count ERROR/FATAL/CRITICAL occurrences for high error rate detection
            error_matches = enhanced_patterns['high_error_rate']['pattern'].findall(log_text)
            if len(error_matches) > 5:  # Threshold for high error rate
                anomalies.append(AnomalyResult(
                    index=len(anomalies),
                    type='high_error_rate',
                    severity='MEDIUM',
                    data={'error_count': len(error_matches), 'threshold': 5},
                    pattern_matched='high_error_rate'
                ))
            
            # Check other patterns
            for pattern_name, pattern_info in enhanced_patterns.items():
                if pattern_name == 'high_error_rate':  # Already handled above
                    continue
                    
                matches = pattern_info['pattern'].findall(log_text)
                if matches:
                    anomalies.append(AnomalyResult(
                        index=len(anomalies),
                        type=f'pattern_{pattern_name}',
                        severity=pattern_info['severity'],
                        data={'matches': matches, 'description': pattern_info['description']},
                        pattern_matched=pattern_name
                    ))
        
        # Check individual log entries for specific anomalies
        for i, log_entry in enumerate(processed_data):
            message = log_entry.get('message', str(log_entry))
            
            # Check for specific critical patterns in individual entries
            if re.search(r'(FATAL|CRITICAL)', message, re.IGNORECASE):
                anomalies.append(AnomalyResult(
                    index=i,
                    type='critical_error',
                    severity='CRITICAL',
                    data=log_entry,
                    pattern_matched='critical_error'
                ))
            
            # Check for connection/timeout patterns
            if re.search(r'(timeout|connection.*failed|refused)', message, re.IGNORECASE):
                anomalies.append(AnomalyResult(
                    index=i,
                    type='connection_issue',
                    severity='HIGH',
                    data=log_entry,
                    pattern_matched='connection_issue'
                ))
        
        return anomalies

    def detect_anomalies(self, processed_data: List[Dict[str, Any]], use_ml=True, use_patterns=True) -> Dict[str, Any]:
        anomalies = []
        if use_ml:
            anomalies.extend(self.detect_anomalies_ensemble(processed_data))
        if use_patterns:
            anomalies.extend(self.detect_enhanced_patterns(processed_data))
        anomalies = self.deduplicate_anomalies(anomalies)
        
        # Convert anomalies to the format expected by the template
        anomalies_detected = []
        for anomaly in anomalies:
            anomaly_obj = {
                'severity': anomaly.severity,
                'type': anomaly.type,
                'message': self._generate_anomaly_message(anomaly),
                'details': anomaly.data,
                'pattern_matched': anomaly.pattern_matched,
                'index': anomaly.index
            }
            anomalies_detected.append(anomaly_obj)
        
        return {
            'anomalies': [anomaly.__dict__ for anomaly in anomalies],  # Keep original format
            'anomalies_detected': anomalies_detected,  # Add template-compatible format
            'count': len(anomalies),
            'total_anomalies': len(anomalies),  # Add for template compatibility
            'summary': f'Detected {len(anomalies)} anomalies'
        }
    
    def _generate_anomaly_message(self, anomaly) -> str:
        """Generate a human-readable message for an anomaly."""
        if anomaly.pattern_matched == 'memory_exhaustion':
            return "Critical memory exhaustion detected - immediate attention required"
        elif anomaly.pattern_matched == 'database_timeout':
            return "Database timeout issues detected - check connection and query performance"
        elif anomaly.pattern_matched == 'authentication_failure':
            return "Authentication failures detected - verify credentials and access policies"
        elif anomaly.pattern_matched == 'high_error_rate':
            return f"High error rate detected - {anomaly.data.get('error_count', 0)} errors found"
        elif anomaly.pattern_matched == 'critical_error':
            return "Critical system error detected - requires immediate investigation"
        elif anomaly.pattern_matched == 'connection_issue':
            return "Connection issues detected - check network connectivity and service availability"
        else:
            return f"Anomaly detected: {anomaly.type}"

# Standalone function for backward compatibility
def detect_anomalies(processed_data: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
    """Standalone function for anomaly detection for backward compatibility."""
    detector = AdvancedAnomalyDetector()
    return detector.detect_anomalies(processed_data, **kwargs)

# Example usage
if __name__ == "__main__":
    processed_data = []  # Load or preprocess your data here
    detector = AdvancedAnomalyDetector()
    results = detector.detect_anomalies(processed_data)
    print(results)