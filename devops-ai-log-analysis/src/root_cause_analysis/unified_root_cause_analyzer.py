import re
import numpy as np
from collections import Counter, defaultdict, deque
from datetime import datetime, timedelta
import networkx as nx
from sklearn.cluster import KMeans
from sklearn.feature_extraction.text import TfidfVectorizer
import pandas as pd
import json
from sklearn.metrics import confusion_matrix
from typing import Dict, List, Any

class UnifiedRootCauseAnalyzer:
    """Unified Root Cause Analysis Module combining advanced, intelligent, and enhanced functionalities."""
    
    def __init__(self, knowledge_base_path: str = "knowledge_base.json"):
        self.knowledge_base = self._build_knowledge_base(knowledge_base_path)
        self.correlation_graph = nx.DiGraph()
        self.pattern_vectorizer = TfidfVectorizer(max_features=500, stop_words='english')
        self.analysis_history = []
        self.common_patterns = self._load_common_patterns()

    def _build_knowledge_base(self, path: str):
        """Build comprehensive knowledge base for root cause analysis"""
        try:
            with open(path, 'r') as file:
                return json.load(file)
        except Exception as e:
            print(f"Error loading knowledge base: {e}")
            return {}

    def _load_common_patterns(self) -> Dict[str, List[str]]:
        """Load common error patterns for different technologies."""
        return {
            'java': [
                r'java\.lang\.OutOfMemoryError',
                r'java\.sql\.SQLException',
                r'java\.net\.ConnectException',
                r'java\.lang\.NullPointerException',
                r'java\.util\.concurrent\.TimeoutException'
            ],
            'python': [
                r'MemoryError',
                r'ConnectionError',
                r'TimeoutError',
                r'AttributeError',
                r'ImportError',
                r'KeyError'
            ],
            'database': [
                r'Lock wait timeout exceeded',
                r'Deadlock found',
                r'Connection timed out',
                r'Too many connections'
            ]
        }

    def analyze_root_cause(self, classified_errors):
        """Analyze root causes based on classified errors."""
        root_causes = {}
        for error in classified_errors:
            cause = self._analyze_error(error)
            if cause in root_causes:
                root_causes[cause].append(error)
            else:
                root_causes[cause] = [error]
        return root_causes

    def _analyze_error(self, error):
        """Analyze individual error for root cause."""
        # Placeholder for actual analysis logic
        return error.get('cause', 'Unknown')

    def generate_recommendations(self, root_causes):
        """Generate actionable recommendations based on root causes."""
        recommendations = {}
        for cause, errors in root_causes.items():
            recommendations[cause] = {
                'steps': self.knowledge_base.get(cause, {}).get('investigation_steps', []),
                'errors': errors
            }
        return recommendations

    def evaluate_analysis(self, true_causes):
        """Evaluate root cause analysis against true causes."""
        predicted_causes = [self._analyze_error(error) for error in self.analysis_history]
        cm = confusion_matrix(true_causes, predicted_causes)
        return cm

    def advanced_analysis(self, errors):
        """Perform advanced AI-powered analysis."""
        # Placeholder for advanced analysis logic
        return {}

    def intelligent_analysis(self, errors):
        """Perform intelligent analysis with AI-powered suggestions."""
        # Placeholder for intelligent analysis logic
        return {}

    def enhanced_analysis(self, errors):
        """Perform enhanced analysis using JSON knowledge base."""
        # Placeholder for enhanced analysis logic
        return {}

    def analyze_log_text(self, log_text: str) -> Dict[str, Any]:
        """Analyze raw log text and extract root causes."""
        extracted_errors = []
        categories = {}
        total_errors = 0

        # Enhanced pattern matching for better detection (with DevOps, CI/CD, SonarQube, etc.)
        enhanced_patterns = {
            'database': [
                r'Database connection timeout', r'SQLException', r'Connection timed out', r'Lock wait timeout exceeded', r'Deadlock found', r'Too many connections', r'Connection pool exhausted', r'Failed to execute query', r'Database connection failed', r'Database service is down',
                r'\[Database\].*timeout', r'\[Database\].*connection', r'postgres://.*timeout'
            ],
            'java': [
                r'java\.lang\.OutOfMemoryError', r'java\.sql\.SQLException', r'java\.net\.ConnectException', r'java\.lang\.NullPointerException', r'java\.util\.concurrent\.TimeoutException', r'Exception in thread', r'at\s+[a-zA-Z0-9_.]+\([^)]*\)'
            ],
            'python': [
                r'MemoryError', r'ConnectionError', r'TimeoutError', r'AttributeError', r'ImportError', r'KeyError', r'Traceback \(most recent call last\)', r'File ".*", line \d+'
            ],
            'network': [
                r'Service unavailable', r'high load', r'HTTP \d{3}', r'Connection refused', r'Network timeout', r'Host unreachable',
                r'\[Network\].*timeout', r'\[Network\].*socket', r'Socket timeout'
            ],
            'application': [
                r'Cache miss', r'Health check failed', r'Service is down', r'Application starting', r'FATAL', r'CRITICAL', r'ERROR', r'WARN',
                r'\[Application\].*failed', r'\[Application\].*authenticate', r'Failed to authenticate'
            ],
            'jenkins': [
                r'SonarQube Quality Gate failed', r'SonarQube analysis did not pass', r'SonarQube.*condition[s]? not met', r'Quality Gate failed',
                r'Jenkins pipeline failed', r'Jenkins.*stage failed', r'Jenkins.*job failed', r'Pipeline.*failed at stage', r'Jenkins.*build failed',
                r'\[Jenkins\].*failed', r'\[Jenkins\].*build', r'\[Jenkins\].*pipeline', r'compilation error', r'Build failed'
            ],
            'sonarqube': [
                r'\[SonarQube\].*failed', r'\[StaticAnalysis\].*SonarQube', r'SonarQube.*quality gate', r'quality gate.*failed', r'critical issues found'
            ],
            'codecoverage': [
                r'\[CodeCoverage\].*threshold', r'Coverage threshold not met', r'coverage.*below', r'minimum required.*%'
            ],
            'staticanalysis': [
                r'\[StaticAnalysis\].*failed', r'Static analysis failed', r'Static code analysis.*critical issues', r'Static analysis tool error', r'Code analysis failed', r'Code smells detected', r'Security scan failed'
            ],
            'diskspace': [
                r'\[DiskSpace\].*exhausted', r'Disk space exhausted', r'no space left', r'0 bytes remaining', r'disk.*full'
            ],
            'monitoring': [
                r'\[Monitoring\].*unreachable', r'Prometheus.*unreachable', r'metrics endpoint.*unreachable', r'monitoring.*down'
            ],
            'docker': [
                r'\[Docker\].*failed', r"Container.*failed to start", r'port.*already in use', r'docker.*error'
            ],
            'kubernetes': [
                r'\[Kubernetes\].*CrashLoopBackOff', r'Pod.*CrashLoopBackOff', r'container exited with code', r'k8s.*error'
            ],
            'loadbalancer': [
                r'\[LoadBalancer\].*failed', r'Health check failed', r'backend server.*failed'
            ],
            'ssl': [
                r'\[SSL\].*expired', r'Certificate expired', r'certificate.*invalid', r'ssl.*error'
            ],
            'backup': [
                r'\[Backup\].*failed', r'Daily backup failed', r'backup.*permissions', r'backup.*error'
            ],
            'api': [
                r'\[API\].*exceeded', r'Rate limit exceeded', r'api.*timeout', r'requests/hour'
            ],
            'cache': [
                r'\[Cache\].*lost', r'Redis connection lost', r'cache.*connection', r'NOAUTH Authentication'
            ],
            'queue': [
                r'\[Queue\].*failed', r'RabbitMQ.*failed', r'message processing failed', r"queue.*not found"
            ],
            'cdn': [
                r'\[CDN\].*error', r'CloudFront.*error', r'distribution error', r'origin server timeout'
            ],
            'logging': [
                r'\[Logging\].*down', r'Log aggregation.*down', r'Elasticsearch.*unavailable', r'logging.*error'
            ],
            'testing': [
                r'\[Testing\].*failed', r'Unit test.*failed', r'test suite failed', r'tests failing'
            ],
            'deployment': [
                r'\[Deployment\].*failed', r'Helm chart.*failed', r'deployment.*error', r'values.yaml.*error'
            ],
            'github': [
                r'\[GitHub\].*failed', r'CI/CD pipeline failed', r'github.*error', r'pipeline.*security-scan'
            ],
            'terraform': [
                r'\[Terraform\].*failed', r'Infrastructure provisioning failed', r'terraform.*error', r'AWS quota exceeded'
            ],
            'artifactory': [
                r'\[Artifactory\].*failed', r'Maven dependencies.*failed', r'repository unavailable', r'artifactory.*error'
            ],
            'security': [
                r'\[Security\].*access', r'Unauthorized access', r'security.*violation', r'access.*denied'
            ],
            'system': [
                r'\[System\].*memory', r'High memory usage', r'memory.*utilized', r'system.*error'
            ]
        }

        # Extract log lines and analyze each one
        log_lines = log_text.split('\n')
        error_lines = []
        for line_num, line in enumerate(log_lines):
            line = line.strip()
            if not line:
                continue
            if any(indicator in line.upper() for indicator in ['ERROR', 'FATAL', 'CRITICAL', 'EXCEPTION', 'FAILED']):
                error_lines.append({
                    'line_number': line_num + 1,
                    'content': line,
                    'timestamp': self._extract_timestamp(line),
                    'level': self._extract_log_level(line),
                    'component': self._extract_component(line)
                })

        # Assign each error line to ALL matching categories, but only add to extracted_errors once (first match)
        devops_priority = [
            'sonarqube', 'jenkins', 'codecoverage', 'staticanalysis', 'diskspace', 'monitoring', 'docker', 'kubernetes', 
            'loadbalancer', 'ssl', 'backup', 'api', 'cache', 'queue', 'cdn', 'logging', 'testing', 'deployment', 
            'github', 'terraform', 'artifactory', 'security', 'system'
        ]
        generic_priority = ['database', 'java', 'python', 'network', 'application', 'general']
        pattern_priority = devops_priority + generic_priority

        for idx, error_line in enumerate(error_lines):
            matched_any = False
            matched_categories = []
            for pattern_category in pattern_priority:
                patterns = enhanced_patterns.get(pattern_category, [])
                for pattern in patterns:
                    if re.search(pattern, error_line['content'], re.IGNORECASE):
                        severity = self._calculate_severity(error_line['level'], pattern_category)
                        error_obj = {
                            'message': error_line['content'],
                            'pattern': pattern,
                            'category': pattern_category,
                            'line_number': error_line['line_number'],
                            'timestamp': error_line['timestamp'],
                            'level': error_line['level'],
                            'component': error_line['component'],
                            'severity': severity
                        }
                        if pattern_category not in categories:
                            categories[pattern_category] = []
                        categories[pattern_category].append(error_obj)
                        matched_categories.append(pattern_category)
                        matched_any = True
            if matched_any:
                # Only add to extracted_errors once per error line (first matched category)
                first_category = matched_categories[0] if matched_categories else None
                if first_category:
                    error_obj = categories[first_category][-1]
                    extracted_errors.append(error_obj)
                    total_errors += 1
            else:
                # Will be handled below as 'general'
                pass

        # If any error lines remain uncategorized, assign them to 'general'
        categorized_lines = set()
        for cat_errors in categories.values():
            for err in cat_errors:
                categorized_lines.add((err['line_number'], err['message']))
        uncategorized_errors = [error_line for error_line in error_lines if (error_line['line_number'], error_line['content']) not in categorized_lines]
        if uncategorized_errors:
            for error_line in uncategorized_errors:
                severity = self._calculate_severity(error_line['level'], 'general')
                error_obj = {
                    'message': error_line['content'],
                    'pattern': 'general_error',
                    'category': 'general',
                    'line_number': error_line['line_number'],
                    'timestamp': error_line['timestamp'],
                    'level': error_line['level'],
                    'component': error_line['component'],
                    'severity': severity
                }
                if 'general' not in categories:
                    categories['general'] = []
                categories['general'].append(error_obj)
                extracted_errors.append(error_obj)
                total_errors += 1

        # Calculate severity scores
        severity_scores = {}
        for category, errors in categories.items():
            severity_scores[category] = self._calculate_category_severity(category, errors)

        # Generate cause frequency and priority scores for template compatibility
        cause_frequency = {}
        priority_scores = {}
        for category, errors in categories.items():
            cause_frequency[category] = len(errors)
            priority_scores[category] = severity_scores.get(category, 3.0)

        if total_errors > 0:
            if not cause_frequency:
                cause_frequency = {'general_errors': total_errors}
                priority_scores = {'general_errors': 5.0}

        # Generate recommendations (per unique error message)
        recommendations = self._generate_recommendations(categories, severity_scores, extracted_errors=extracted_errors)

        return {
            'extracted_errors': extracted_errors,
            'top_errors': extracted_errors[:10],
            'summary': f"Found {total_errors} errors across {len(categories)} categories in log text.",
            'total_errors': total_errors,
            'categories': categories,
            'severity_scores': severity_scores,
            'errors_found': total_errors,
            'patterns': {
                'technology_detected': list(categories.keys()),
                'common_issues': list(cause_frequency.keys())[:5]
            },
            'error_timeline': sorted([e for e in extracted_errors if e.get('timestamp')], key=lambda x: x['timestamp'] or ''),
            'components_affected': list(set([e.get('component', 'unknown') for e in extracted_errors])),
            'cause_frequency': cause_frequency,
            'priority_scores': priority_scores,
            'recommendations': recommendations
        }
    
    def _extract_timestamp(self, line: str) -> str:
        """Extract timestamp from log line."""
        timestamp_patterns = [
            r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}',
            r'\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}',
            r'\d{2}-\d{2}-\d{4} \d{2}:\d{2}:\d{2}'
        ]
        for pattern in timestamp_patterns:
            match = re.search(pattern, line)
            if match:
                return match.group()
        return None
    
    def _extract_log_level(self, line: str) -> str:
        """Extract log level from line."""
        levels = ['FATAL', 'CRITICAL', 'ERROR', 'WARN', 'INFO', 'DEBUG']
        for level in levels:
            if level in line.upper():
                return level
        return 'UNKNOWN'
    
    def _extract_component(self, line: str) -> str:
        """Extract component name from log line."""
        # Look for [component] pattern
        match = re.search(r'\[([^\]]+)\]', line)
        if match:
            return match.group(1)
        return 'unknown'
    
    def _calculate_category_severity(self, category: str, errors: List) -> float:
        """Calculate severity score for a category."""
        base_scores = {
            'database': 8.0,
            'java': 7.0,
            'python': 6.0,
            'network': 5.0,
            'application': 4.0,
            'general': 3.0
        }
        
        base_score = base_scores.get(category, 3.0)
        error_count = len(errors)
        
        # Severity modifiers based on error levels
        critical_count = sum(1 for e in errors if e.get('level') in ['FATAL', 'CRITICAL'])
        error_count_level = sum(1 for e in errors if e.get('level') == 'ERROR')
        
        severity_multiplier = 1.0
        if critical_count > 0:
            severity_multiplier += 0.5
        if error_count_level > 5:
            severity_multiplier += 0.3
        if error_count > 10:
            severity_multiplier += 0.2
            
        return min(10.0, base_score * severity_multiplier)

    def _calculate_severity(self, log_level: str, category: str) -> str:
        """Calculate severity score for an error based on level and category."""
        # Base severity from log level
        level_severity = {
            'FATAL': 100,
            'CRITICAL': 90,
            'ERROR': 70,
            'WARN': 50,
            'INFO': 30,
            'DEBUG': 10
        }
        
        # Category multipliers
        category_multipliers = {
            'database': 1.2,
            'security': 1.3,
            'memory': 1.2,
            'performance': 1.1,
            'authentication': 1.2,
            'network': 1.1,
            'general': 1.0
        }
        
        base_score = level_severity.get(log_level.upper(), 50)
        multiplier = category_multipliers.get(category.lower(), 1.0)
        final_score = min(100, int(base_score * multiplier))
        
        # Convert to severity labels
        if final_score >= 90:
            return 'CRITICAL'
        elif final_score >= 70:
            return 'HIGH'
        elif final_score >= 50:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _generate_recommendations(self, categories: Dict[str, List], severity_scores: Dict[str, float], extracted_errors: list = None) -> List[Dict[str, Any]]:
        """Always use rule-based recommendations for 1:1 mapping in demo."""
        if extracted_errors is None:
            # Flatten all errors if not provided
            extracted_errors = []
            for cat_errors in categories.values():
                extracted_errors.extend(cat_errors)
        return self._generate_rule_based_recommendations(categories, severity_scores, extracted_errors=extracted_errors)
    
    def _create_error_context_for_recommendations(self, categories: Dict[str, List], severity_scores: Dict[str, float]) -> str:
        """Create a comprehensive context string for LLM analysis."""
        context_parts = []
        
        # Summary
        total_errors = sum(len(errors) for errors in categories.values())
        context_parts.append(f"LOG ANALYSIS SUMMARY: Found {total_errors} errors across {len(categories)} categories.")
        
        # Error categories with counts and severity
        context_parts.append("\nERROR CATEGORIES:")
        for category, errors in categories.items():
            severity = severity_scores.get(category, 0)
            sample_errors = errors[:2]  # Show sample errors
            context_parts.append(f"- {category.upper()}: {len(errors)} errors (severity: {severity:.1f}/10)")
            for error in sample_errors:
                if isinstance(error, dict):
                    msg = error.get('message', str(error))[:80]
                else:
                    msg = str(error)[:80]
                context_parts.append(f"  Example: {msg}...")
        
        return '\n'.join(context_parts)
    
    def _generate_llm_recommendations(self, llm_processor, error_context: str, extracted_errors: list = None) -> List[Dict[str, Any]]:
        """Generate recommendations using LLM."""
        try:
            import json
            
            prompt = f"""You are a senior DevOps engineer analyzing system errors. Based on the following log analysis, provide 3-5 specific, actionable recommendations to resolve the issues.

{error_context}

For each recommendation, provide:
1. A clear, specific title (max 50 chars)
2. A brief summary description (1-2 sentences explaining WHAT needs to be done and WHY)
3. Detailed step-by-step actions as separate items (3-5 specific steps with commands/configurations)
4. Priority level (CRITICAL, HIGH, MEDIUM, LOW)
5. Estimated effort (QUICK, MODERATE, EXTENSIVE)
6. Category (IMMEDIATE, MONITORING, INFRASTRUCTURE, CODE, SECURITY)
7. Specific reasoning based on the error patterns

Important: 
- Keep description brief and high-level (the WHY and WHAT)
- Put detailed HOW-TO instructions in separate steps array
- Each step should be actionable with specific commands where possible
- Include exact commands, file paths, configuration changes in the steps

Format your response as a JSON array of recommendation objects with these exact keys:
- title
- description (brief summary)
- steps (array of detailed action items)
- priority
- effort
- category
- reasoning

Example format:
[
  {{
    "title": "Fix Database Connection Pool",
    "description": "Database connection pool is exhausted causing timeouts. Increase pool size and restart service.",
    "steps": [
      "Edit /etc/database/config.yaml and set max_connections: 50",
      "Restart database service: sudo systemctl restart postgresql",
      "Monitor connections: SELECT count(*) FROM pg_stat_activity",
      "Set up alerts for pool usage above 80%"
    ],
    "priority": "HIGH",
    "effort": "QUICK",
    "category": "IMMEDIATE",
    "reasoning": "Connection pool exhaustion is causing database timeouts and cascading failures"
  }}
]"""

            response = llm_processor.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are an expert DevOps engineer providing actionable recommendations for log analysis issues. Always respond with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                user=json.dumps({"appkey": llm_processor.appkey}),
                temperature=0.7,
                max_tokens=2000
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Try to extract JSON from the response
            import re
            # Look for JSON array in the response
            json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
            if json_match:
                recommendations_json = json.loads(json_match.group())
                # Validate and format recommendations
                formatted_recommendations = []
                extracted_errors = extracted_errors or []
                for i, rec in enumerate(recommendations_json):
                    if isinstance(rec, dict) and 'title' in rec and 'description' in rec:
                        steps = rec.get('steps', [])
                        if not steps:
                            steps = [rec.get('description', 'No specific steps provided')]
                        # Attach the best-match error string/log line
                        error_message = None
                        error_line_number = None
                        error_category = None
                        severity = None
                        # Find best match: try multiple strategies to match errors to recommendations
                        best_error = None
                        if extracted_errors:
                            rec_text = (rec.get('title', '') + ' ' + rec.get('description', '')).lower()
                            
                            # Strategy 1: Find error by component/technology keywords
                            component_keywords = {
                                'database': ['database', 'db', 'postgres', 'mysql', 'connection', 'timeout'],
                                'jenkins': ['jenkins', 'build', 'pipeline', 'maven', 'compilation'],
                                'application': ['auth', 'token', 'login', 'session', 'user'],
                                'memory': ['memory', 'heap', 'oom', 'gc'],
                                'network': ['network', 'connection', 'timeout', 'socket']
                            }
                            
                            for err in extracted_errors:
                                err_msg = (err.get('message') or '').lower()
                                err_component = (err.get('component') or '').lower()
                                err_category = (err.get('category') or '').lower()
                                
                                # Check if recommendation mentions error's component or category
                                if err_component and err_component in rec_text:
                                    best_error = err
                                    break
                                elif err_category and err_category in rec_text:
                                    best_error = err
                                    break
                                
                                # Check for keyword matches
                                for category, keywords in component_keywords.items():
                                    if category in rec_text and any(kw in err_msg for kw in keywords):
                                        best_error = err
                                        break
                                if best_error:
                                    break
                            
                            # Strategy 2: If no match, assign errors cyclically to avoid all getting the first one
                            if not best_error:
                                best_error = extracted_errors[i % len(extracted_errors)]
                            
                            error_message = best_error.get('message')
                            error_line_number = best_error.get('line_number')
                            error_category = best_error.get('category')
                            severity = best_error.get('severity')
                        else:
                            error_message = 'No specific error found for this recommendation'
                        formatted_rec = {
                            'problem': rec.get('title', f'Recommendation {i+1}'),
                            'description': rec.get('description', 'No description provided'),
                            'priority': rec.get('priority', 'MEDIUM'),
                            'effort': rec.get('effort', 'MODERATE'),
                            'category': rec.get('category', 'GENERAL'),
                            'reasoning': rec.get('reasoning', 'Based on log analysis patterns'),
                            'source': 'knowledge_base',
                            'steps': steps,
                            'error_message': error_message,
                            'error_line_number': error_line_number,
                            'error_category': error_category,
                            'severity': severity
                        }
                        formatted_recommendations.append(formatted_rec)
                return formatted_recommendations[:5]  # Limit to 5 recommendations
            
        except Exception as e:
            print(f"LLM recommendation generation error: {e}")
        
        return []
    
    def _generate_rule_based_recommendations(self, categories: Dict[str, List], severity_scores: Dict[str, float], extracted_errors: list = None) -> List[Dict[str, Any]]:
        """Generate recommendations for each unique error using enhanced rule-based logic as fallback."""
        recommendations = []
        # Enhanced recommendation templates
        recommendation_templates = {
            'database': {
                'problem': 'Database Connection & Performance Issues',
                'priority': 'HIGH',
                'effort': 'MODERATE',
                'category': 'INFRASTRUCTURE',
                'source': 'knowledge_base',
                'steps': [
                    'Check database connection pool configuration and increase pool size if needed',
                    'Verify database server availability and health metrics',
                    'Review and optimize slow queries causing timeouts',
                    'Implement connection retry logic with exponential backoff',
                    'Set up database monitoring and alerting for connection issues'
                ],
                'reasoning': 'Database errors can cascade and cause system-wide service disruptions'
            },
            'python': {
                'problem': 'Memory Management & Runtime Issues',
                'priority': 'CRITICAL',
                'effort': 'EXTENSIVE',
                'category': 'CODE',
                'source': 'knowledge_base',
                'steps': [
                    'Analyze memory usage patterns and implement proper garbage collection',
                    'Review application code for memory leaks and inefficient algorithms',
                    'Increase JVM/Python heap size and optimize memory allocation',
                    'Implement memory profiling and monitoring tools',
                    'Add circuit breakers to prevent memory exhaustion cascades'
                ],
                'reasoning': 'Memory issues lead to application crashes and degraded performance'
            },
            'java': {
                'problem': 'JVM Runtime & Exception Handling',
                'priority': 'HIGH',
                'effort': 'MODERATE',
                'category': 'CODE',
                'source': 'knowledge_base',
                'steps': [
                    'Review JVM configuration and garbage collection settings',
                    'Implement proper exception handling and error recovery',
                    'Analyze thread dumps and heap dumps for bottlenecks',
                    'Optimize application startup and resource initialization',
                    'Add comprehensive logging and monitoring for Java processes'
                ],
                'reasoning': 'JVM issues can cause performance degradation and application instability'
            },
            'network': {
                'problem': 'Network Connectivity & Service Communication',
                'priority': 'HIGH',
                'effort': 'QUICK',
                'category': 'IMMEDIATE',
                'source': 'knowledge_base',
                'steps': [
                    'Verify network connectivity between all services and dependencies',
                    'Check firewall rules, security groups, and network ACLs',
                    'Implement health checks and service discovery mechanisms',
                    'Add network monitoring and latency tracking',
                    'Configure proper timeout and retry policies for external calls'
                ],
                'reasoning': 'Network issues prevent critical operations and degrade user experience'
            },
            'application': {
                'problem': 'Application Runtime & Configuration Issues',
                'priority': 'MEDIUM',
                'effort': 'MODERATE',
                'category': 'CODE',
                'source': 'knowledge_base',
                'steps': [
                    'Review application configuration and environment variables',
                    'Implement comprehensive error handling and logging',
                    'Add health checks and readiness probes for containers',
                    'Monitor application metrics and set up proper alerting',
                    'Implement graceful degradation and fallback mechanisms'
                ],
                'reasoning': 'Application errors indicate code or configuration issues that need attention'
            }
        }
        
        # If extracted_errors is provided, generate a recommendation for each unique error
        if extracted_errors:
            for error in extracted_errors:
                category = error.get('category', 'application')
                severity = error.get('severity', 'MEDIUM')
                template_key = 'application'  # default
                for key in recommendation_templates.keys():
                    if key in category.lower():
                        template_key = key
                        break
                template = recommendation_templates.get(template_key, recommendation_templates['application']).copy()
                template['error_message'] = error.get('message')
                template['error_line_number'] = error.get('line_number')
                template['error_category'] = category
                template['severity'] = severity
                template['problem'] = f"{template['problem']} (Line {error.get('line_number')}, Severity {severity})"
                template['source'] = 'knowledge_base'
                recommendations.append(template)
        else:
            # Fallback: Generate recommendations for each detected category (legacy)
            for category, errors in categories.items():
                if not errors:
                    continue
                error_count = len(errors)
                severity = severity_scores.get(category, 5.0)
                template_key = 'application'  # default
                for key in recommendation_templates.keys():
                    if key in category.lower():
                        template_key = key
                        break
                if template_key in recommendation_templates:
                    template = recommendation_templates[template_key].copy()
                    template['error_count'] = error_count
                    template['severity_score'] = severity
                    template['cause'] = category
                    if severity >= 8.0 or error_count > 5:
                        template['priority'] = 'CRITICAL'
                    elif severity >= 6.0 or error_count > 2:
                        template['priority'] = 'HIGH'
                    else:
                        template['priority'] = 'MEDIUM'
                    template['problem'] = f"{template['problem']} ({error_count} errors, severity {severity:.1f})"
                    template['source'] = 'knowledge_base'
                    recommendations.append(template)
        
        # Add security recommendation for authentication issues
        auth_categories = [cat for cat in categories.keys() if 'auth' in cat.lower() or 'login' in cat.lower()]
        if auth_categories:
            total_auth_errors = sum(len(categories[cat]) for cat in auth_categories)
            recommendations.append({
                'problem': f'Authentication Security Review ({total_auth_errors} failures)',
                'priority': 'HIGH',
                'effort': 'MODERATE',
                'category': 'SECURITY',
                'error_count': total_auth_errors,
                'severity_score': 7.0,
                'source': 'knowledge_base',  # Add source field
                'steps': [
                    'Review authentication failure patterns and implement account lockout policies',
                    'Check for brute force attacks and implement rate limiting',
                    'Verify SSL/TLS configuration and certificate validity',
                    'Audit user permissions and access control policies',
                    'Implement multi-factor authentication for sensitive operations'
                ],
                'reasoning': 'Authentication failures may indicate security threats or misconfigurations'
            })
        
        # Add monitoring recommendation for high error rates
        total_errors = sum(len(errors) for errors in categories.values())
        if total_errors > 10:
            recommendations.append({
                'problem': f'High Error Rate Monitoring ({total_errors} total errors)',
                'priority': 'HIGH',
                'effort': 'QUICK',
                'category': 'MONITORING',
                'error_count': total_errors,
                'severity_score': 8.0,
                'source': 'knowledge_base',  # Add source field
                'steps': [
                    'Implement real-time error rate monitoring and alerting',
                    'Set up log aggregation and centralized error tracking',
                    'Create dashboards for error trends and patterns',
                    'Establish error rate thresholds and escalation procedures',
                    'Implement automated incident response for critical errors'
                ],
                'reasoning': 'High error rates indicate systemic issues requiring immediate monitoring'
            })
        
        return recommendations  # No artificial limit, return all

# Example usage
if __name__ == "__main__":
    classified_errors = []  # Load or preprocess your classified errors here
    analyzer = UnifiedRootCauseAnalyzer()
    root_causes = analyzer.analyze_root_cause(classified_errors)
    recommendations = analyzer.generate_recommendations(root_causes)
    print(recommendations)