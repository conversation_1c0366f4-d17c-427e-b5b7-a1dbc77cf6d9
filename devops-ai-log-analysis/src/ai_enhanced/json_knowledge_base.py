#!/usr/bin/env python3
"""
Generic JSON-based Knowledge Base Framework for Root Cause Analysis
Teams can customize this by providing their own knowledge_base.json file
"""

import json
import re
import os
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass, asdict
from collections import defaultdict

@dataclass
class KnowledgeEntry:
    """Represents a knowledge base entry with problem, cause, and solution."""
    id: str
    category: str
    problem: str
    symptoms: List[str]
    root_causes: List[str]
    solutions: List[str]
    prevention: List[str]
    keywords: List[str]
    severity: str
    frequency: str
    tools: List[str]
    related_issues: List[str]
    documentation_links: List[str] = None
    last_updated: str = None

class JSONKnowledgeBase:
    """Generic JSON-based knowledge base framework for root cause analysis."""
    
    def __init__(self, json_file_path: str = "knowledge_base.json"):
        self.json_file_path = json_file_path
        self.knowledge_entries: Dict[str, KnowledgeEntry] = {}
        self.category_index: Dict[str, List[str]] = defaultdict(list)
        self.keyword_index: Dict[str, List[str]] = defaultdict(list)
        self.severity_index: Dict[str, List[str]] = defaultdict(list)
        self.metadata: Dict[str, Any] = {}
        self.load_from_json()
    
    def load_from_json(self):
        """Load knowledge base from JSON file."""
        try:
            if not os.path.exists(self.json_file_path):
                self._create_default_json()
            
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Load metadata
            self.metadata = data.get('metadata', {})
            
            # Load entries
            for entry_data in data.get('entries', []):
                entry = KnowledgeEntry(
                    id=entry_data['id'],
                    category=entry_data['category'],
                    problem=entry_data['problem'],
                    symptoms=entry_data['symptoms'],
                    root_causes=entry_data['root_causes'],
                    solutions=entry_data['solutions'],
                    prevention=entry_data['prevention'],
                    keywords=entry_data['keywords'],
                    severity=entry_data['severity'],
                    frequency=entry_data['frequency'],
                    tools=entry_data['tools'],
                    related_issues=entry_data['related_issues'],
                    documentation_links=entry_data.get('documentation_links', []),
                    last_updated=entry_data.get('last_updated')
                )
                self.knowledge_entries[entry.id] = entry
            
            self._build_indexes()
            print(f"✅ Loaded {len(self.knowledge_entries)} entries from {self.json_file_path}")
            
        except Exception as e:
            print(f"❌ Error loading knowledge base: {e}")
            self._create_default_json()
    
    def _create_default_json(self):
        """Create a default JSON file with sample entries."""
        default_data = {
            "metadata": {
                "name": "Default Knowledge Base",
                "version": "1.0.0",
                "description": "Default knowledge base for DevOps troubleshooting",
                "created_date": datetime.now().strftime("%Y-%m-%d"),
                "last_updated": datetime.now().strftime("%Y-%m-%d"),
                "team": "DevOps Team",
                "contact": "<EMAIL>"
            },
            "categories": ["General", "Infrastructure", "Application", "Network"],
            "severities": ["critical", "high", "medium", "low"],
            "frequencies": ["common", "uncommon", "rare"],
            "entries": [
                {
                    "id": "general_001",
                    "category": "General",
                    "problem": "Service Unavailable",
                    "symptoms": ["Service not responding", "Connection refused", "Timeout errors"],
                    "root_causes": ["Service down", "Network issues", "Configuration problems"],
                    "solutions": ["Restart service", "Check network connectivity", "Verify configuration"],
                    "prevention": ["Monitor service health", "Use redundancy", "Regular maintenance"],
                    "keywords": ["service", "unavailable", "down", "timeout"],
                    "severity": "high",
                    "frequency": "common",
                    "tools": ["ping", "curl", "service status"],
                    "related_issues": [],
                    "documentation_links": [],
                    "last_updated": datetime.now().strftime("%Y-%m-%d")
                }
            ]
        }
        
        with open(self.json_file_path, 'w', encoding='utf-8') as f:
            json.dump(default_data, f, indent=2, ensure_ascii=False)
        
        print(f"📝 Created default knowledge base at {self.json_file_path}")
    
    def _build_indexes(self):
        """Build search indexes for efficient lookups."""
        self.category_index.clear()
        self.keyword_index.clear()
        self.severity_index.clear()
        
        for entry_id, entry in self.knowledge_entries.items():
            # Category index
            self.category_index[entry.category.lower()].append(entry_id)
            
            # Keyword index
            for keyword in entry.keywords:
                self.keyword_index[keyword.lower()].append(entry_id)
            
            # Severity index
            self.severity_index[entry.severity.lower()].append(entry_id)
    
    def search_by_symptoms(self, symptoms: List[str]) -> List[Tuple[KnowledgeEntry, float]]:
        """Search for knowledge entries by symptoms with relevance scoring."""
        matches = []
        
        for entry in self.knowledge_entries.values():
            score = 0.0
            for symptom in symptoms:
                symptom_words = symptom.lower().split()
                for entry_symptom in entry.symptoms:
                    entry_symptom_words = entry_symptom.lower().split()
                    # Calculate word overlap
                    overlap = len(set(symptom_words) & set(entry_symptom_words))
                    if overlap > 0:
                        score += overlap / len(symptom_words)
            
            if score > 0:
                matches.append((entry, score))
        
        # Sort by relevance score
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches[:10]  # Return top 10
    
    def search_by_keywords(self, keywords: List[str]) -> List[KnowledgeEntry]:
        """Search for knowledge entries by keywords."""
        matching_ids = set()
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            if keyword_lower in self.keyword_index:
                matching_ids.update(self.keyword_index[keyword_lower])
        
        return [self.knowledge_entries[entry_id] for entry_id in matching_ids]
    
    def search_by_category(self, category: str) -> List[KnowledgeEntry]:
        """Search for knowledge entries by category."""
        category_lower = category.lower()
        if category_lower in self.category_index:
            return [self.knowledge_entries[entry_id] for entry_id in self.category_index[category_lower]]
        return []
    
    def search_by_severity(self, severity: str) -> List[KnowledgeEntry]:
        """Search for knowledge entries by severity."""
        severity_lower = severity.lower()
        if severity_lower in self.severity_index:
            return [self.knowledge_entries[entry_id] for entry_id in self.severity_index[severity_lower]]
        return []
    
    def fuzzy_search(self, query: str) -> List[Tuple[KnowledgeEntry, float]]:
        """Perform fuzzy search across all text fields."""
        matches = []
        query_lower = query.lower()
        query_words = query_lower.split()
        
        for entry in self.knowledge_entries.values():
            score = 0.0
            
            # Search in problem description
            if any(word in entry.problem.lower() for word in query_words):
                score += 3.0
            
            # Search in symptoms
            for symptom in entry.symptoms:
                if any(word in symptom.lower() for word in query_words):
                    score += 2.0
            
            # Search in keywords
            for keyword in entry.keywords:
                if keyword.lower() in query_lower:
                    score += 2.5
            
            # Search in solutions
            for solution in entry.solutions:
                if any(word in solution.lower() for word in query_words):
                    score += 1.0
            
            # Search in root causes
            for cause in entry.root_causes:
                if any(word in cause.lower() for word in query_words):
                    score += 1.5
            
            if score > 0:
                matches.append((entry, score))
        
        # Sort by relevance score
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches
    
    def get_recommendations(self, 
                          error_message: str, 
                          category: str = None,
                          severity: str = None,
                          max_results: int = 5) -> List[Dict[str, Any]]:
        """Get recommendations based on error message and filters."""
        recommendations = []
        
        # Extract keywords from error message
        keywords = self._extract_keywords(error_message)
        
        # Perform fuzzy search
        fuzzy_matches = self.fuzzy_search(error_message)
        
        # Search by keywords
        keyword_matches = self.search_by_keywords(keywords)
        
        # Search by symptoms
        symptom_matches = self.search_by_symptoms([error_message])
        
        # Combine all matches with scores
        all_matches = {}
        
        # Add fuzzy matches
        for entry, score in fuzzy_matches:
            all_matches[entry.id] = (entry, score)
        
        # Add keyword matches
        for entry in keyword_matches:
            if entry.id in all_matches:
                all_matches[entry.id] = (entry, all_matches[entry.id][1] + 1.0)
            else:
                all_matches[entry.id] = (entry, 1.0)
        
        # Add symptom matches
        for entry, score in symptom_matches:
            if entry.id in all_matches:
                all_matches[entry.id] = (entry, all_matches[entry.id][1] + score)
            else:
                all_matches[entry.id] = (entry, score)
        
        # Filter by category if specified
        if category:
            all_matches = {k: v for k, v in all_matches.items() 
                          if v[0].category.lower() == category.lower()}
        
        # Filter by severity if specified
        if severity:
            all_matches = {k: v for k, v in all_matches.items() 
                          if v[0].severity.lower() == severity.lower()}
        
        # Sort by relevance score
        sorted_matches = sorted(all_matches.values(), key=lambda x: x[1], reverse=True)
        
        # Convert to recommendation format
        for entry, score in sorted_matches[:max_results]:
            recommendation = {
                'id': entry.id,
                'category': entry.category,
                'problem': entry.problem,
                'relevance_score': round(score, 2),
                'symptoms': entry.symptoms,
                'root_causes': entry.root_causes,
                'solutions': entry.solutions,
                'prevention': entry.prevention,
                'tools': entry.tools,
                'severity': entry.severity,
                'frequency': entry.frequency,
                'related_issues': entry.related_issues,
                'documentation_links': entry.documentation_links or [],
                'last_updated': entry.last_updated
            }
            recommendations.append(recommendation)
        
        return recommendations
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract relevant keywords from text."""
        # Get all keywords from the knowledge base
        all_keywords = set()
        for entry in self.knowledge_entries.values():
            all_keywords.update(entry.keywords)
        
        text_lower = text.lower()
        found_keywords = []
        
        for keyword in all_keywords:
            if keyword.lower() in text_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def get_categories(self) -> List[str]:
        """Get all available categories."""
        return list(self.category_index.keys())
    
    def get_severities(self) -> List[str]:
        """Get all available severities."""
        return list(self.severity_index.keys())
    
    def get_entry_by_id(self, entry_id: str) -> Optional[KnowledgeEntry]:
        """Get a specific knowledge entry by ID."""
        return self.knowledge_entries.get(entry_id)
    
    def get_related_entries(self, entry_id: str) -> List[KnowledgeEntry]:
        """Get related entries for a given entry."""
        entry = self.get_entry_by_id(entry_id)
        if not entry:
            return []
        
        related_entries = []
        for related_id in entry.related_issues:
            related_entry = self.get_entry_by_id(related_id)
            if related_entry:
                related_entries.append(related_entry)
        
        return related_entries
    
    def add_entry(self, entry: KnowledgeEntry):
        """Add a new knowledge entry."""
        self.knowledge_entries[entry.id] = entry
        self._build_indexes()
    
    def update_entry(self, entry: KnowledgeEntry):
        """Update an existing knowledge entry."""
        if entry.id in self.knowledge_entries:
            self.knowledge_entries[entry.id] = entry
            self._build_indexes()
            return True
        return False
    
    def delete_entry(self, entry_id: str):
        """Delete a knowledge entry."""
        if entry_id in self.knowledge_entries:
            del self.knowledge_entries[entry_id]
            self._build_indexes()
            return True
        return False
    
    def save_to_json(self, file_path: str = None):
        """Save knowledge base to JSON file."""
        if file_path is None:
            file_path = self.json_file_path
        
        # Convert entries to dictionaries
        entries_data = []
        for entry in self.knowledge_entries.values():
            entry_dict = asdict(entry)
            entries_data.append(entry_dict)
        
        # Update metadata
        self.metadata['last_updated'] = datetime.now().strftime("%Y-%m-%d")
        
        data = {
            'metadata': self.metadata,
            'categories': self.get_categories(),
            'severities': self.get_severities(),
            'entries': entries_data
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Knowledge base saved to {file_path}")
    
    def import_from_json(self, file_path: str):
        """Import knowledge base from another JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Import entries
            imported_count = 0
            for entry_data in data.get('entries', []):
                entry = KnowledgeEntry(**entry_data)
                self.add_entry(entry)
                imported_count += 1
            
            print(f"📥 Imported {imported_count} entries from {file_path}")
            return imported_count
            
        except Exception as e:
            print(f"❌ Error importing from {file_path}: {e}")
            return 0
    
    def export_category(self, category: str, file_path: str):
        """Export entries from a specific category."""
        category_entries = self.search_by_category(category)
        
        data = {
            'metadata': {
                **self.metadata,
                'export_category': category,
                'export_date': datetime.now().strftime("%Y-%m-%d")
            },
            'entries': [asdict(entry) for entry in category_entries]
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"📤 Exported {len(category_entries)} entries from '{category}' to {file_path}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get knowledge base statistics."""
        stats = {
            'metadata': self.metadata,
            'total_entries': len(self.knowledge_entries),
            'categories': {},
            'severities': {},
            'frequencies': {},
            'most_common_keywords': {}
        }
        
        # Category statistics
        for category, entries in self.category_index.items():
            stats['categories'][category] = len(entries)
        
        # Severity statistics
        for severity, entries in self.severity_index.items():
            stats['severities'][severity] = len(entries)
        
        # Frequency statistics
        frequency_counts = defaultdict(int)
        for entry in self.knowledge_entries.values():
            frequency_counts[entry.frequency] += 1
        stats['frequencies'] = dict(frequency_counts)
        
        # Keyword statistics
        keyword_counts = defaultdict(int)
        for entry in self.knowledge_entries.values():
            for keyword in entry.keywords:
                keyword_counts[keyword] += 1
        
        # Top 10 keywords
        top_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        stats['most_common_keywords'] = dict(top_keywords)
        
        return stats
    
    def validate_knowledge_base(self) -> List[str]:
        """Validate the knowledge base and return any issues found."""
        issues = []
        
        # Check for duplicate IDs
        ids = [entry.id for entry in self.knowledge_entries.values()]
        if len(ids) != len(set(ids)):
            issues.append("Duplicate entry IDs found")
        
        # Check for missing required fields
        for entry in self.knowledge_entries.values():
            if not entry.id:
                issues.append(f"Entry missing ID")
            if not entry.problem:
                issues.append(f"Entry {entry.id} missing problem description")
            if not entry.symptoms:
                issues.append(f"Entry {entry.id} missing symptoms")
            if not entry.solutions:
                issues.append(f"Entry {entry.id} missing solutions")
        
        # Check for invalid related issues
        for entry in self.knowledge_entries.values():
            for related_id in entry.related_issues:
                if related_id not in self.knowledge_entries:
                    issues.append(f"Entry {entry.id} references non-existent related issue: {related_id}")
        
        return issues

# Create global knowledge base instance
def create_knowledge_base(json_file_path: str = "knowledge_base.json") -> JSONKnowledgeBase:
    """Create a knowledge base instance with the specified JSON file."""
    return JSONKnowledgeBase(json_file_path)

# Default instance
knowledge_base = create_knowledge_base()
