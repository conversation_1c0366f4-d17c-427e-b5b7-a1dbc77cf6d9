#!/usr/bin/env python3
"""
RAG (Retrieval-Augmented Generation) System for DevOps Log Analysis

This module implements a RAG system that:
1. Retrieves relevant historical solutions and troubleshooting guides
2. Augments LLM responses with contextual knowledge
3. Provides more accurate and domain-specific recommendations
"""

import os
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import TruncatedSVD
import pickle
import hashlib
from datetime import datetime, timedelta

class DevOpsRAGSystem:
    """RAG System for DevOps Log Analysis with vector search and knowledge retrieval."""
    
    def __init__(self, knowledge_base_dir: str = "rag_knowledge", embedding_dim: int = 300):
        self.knowledge_base_dir = knowledge_base_dir
        self.embedding_dim = embedding_dim
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english', ngram_range=(1, 2))
        self.svd = TruncatedSVD(n_components=embedding_dim)
        
        # Knowledge base storage
        self.documents = []
        self.document_embeddings = None
        self.metadata = []
        
        # Ensure knowledge base directory exists
        os.makedirs(knowledge_base_dir, exist_ok=True)
        
        # Initialize or load existing knowledge base
        self._load_or_initialize_knowledge_base()
    
    def _load_or_initialize_knowledge_base(self):
        """Load existing knowledge base or initialize with default DevOps knowledge."""
        kb_path = os.path.join(self.knowledge_base_dir, "knowledge_base.json")
        embeddings_path = os.path.join(self.knowledge_base_dir, "embeddings.pkl")
        
        if os.path.exists(kb_path) and os.path.exists(embeddings_path):
            self._load_knowledge_base()
        else:
            self._initialize_default_knowledge_base()
            self._build_embeddings()
            self._save_knowledge_base()
    
    def _initialize_default_knowledge_base(self):
        """Initialize with default DevOps troubleshooting knowledge."""
        default_knowledge = [
            {
                "id": "db_timeout_001",
                "category": "database",
                "title": "Database Connection Timeout Resolution",
                "problem": "Database connection timeouts causing application failures",
                "solution": "Increase connection pool size, optimize queries, check network latency",
                "steps": [
                    "Check current connection pool configuration",
                    "Monitor active connections: SELECT count(*) FROM pg_stat_activity",
                    "Increase max_connections in postgresql.conf",
                    "Restart database service",
                    "Implement connection retry logic with exponential backoff"
                ],
                "technologies": ["postgresql", "mysql", "database"],
                "error_patterns": ["connection timeout", "too many connections", "connection pool exhausted"],
                "severity": "high",
                "resolution_time": "30-60 minutes",
                "success_rate": 0.95
            },
            {
                "id": "java_oom_001",
                "category": "memory",
                "title": "Java OutOfMemoryError Resolution",
                "problem": "Java applications running out of heap memory",
                "solution": "Increase heap size, identify memory leaks, optimize garbage collection",
                "steps": [
                    "Analyze heap dump with jhat or Eclipse MAT",
                    "Increase JVM heap size: -Xmx4g -Xms2g",
                    "Enable GC logging: -XX:+PrintGCDetails",
                    "Review application for memory leaks",
                    "Implement memory monitoring and alerts"
                ],
                "technologies": ["java", "jvm", "spring"],
                "error_patterns": ["outofmemoryerror", "java heap space", "gc overhead limit"],
                "severity": "critical",
                "resolution_time": "60-120 minutes",
                "success_rate": 0.88
            },
            {
                "id": "ssl_cert_001",
                "category": "security",
                "title": "SSL Certificate Verification Failed",
                "problem": "SSL handshake failures due to certificate issues",
                "solution": "Verify certificate validity, update CA bundle, check certificate chain",
                "steps": [
                    "Check certificate expiry: openssl x509 -in cert.pem -text -noout",
                    "Verify certificate chain: openssl verify -CAfile ca-bundle.crt cert.pem",
                    "Update system CA certificates",
                    "Restart services using SSL",
                    "Monitor certificate expiration dates"
                ],
                "technologies": ["ssl", "tls", "nginx", "apache"],
                "error_patterns": ["certificate verify failed", "ssl handshake failed", "self signed certificate"],
                "severity": "high",
                "resolution_time": "15-30 minutes",
                "success_rate": 0.92
            },
            {
                "id": "python_import_001",
                "category": "application",
                "title": "Python Import Error Resolution",
                "problem": "Python applications failing due to missing modules or import errors",
                "solution": "Install missing packages, fix PYTHONPATH, resolve version conflicts",
                "steps": [
                    "Check installed packages: pip list",
                    "Install missing package: pip install package_name",
                    "Verify PYTHONPATH environment variable",
                    "Check for version conflicts: pip check",
                    "Use virtual environments to isolate dependencies"
                ],
                "technologies": ["python", "pip", "virtualenv"],
                "error_patterns": ["importerror", "modulenotfounderror", "no module named"],
                "severity": "medium",
                "resolution_time": "10-20 minutes",
                "success_rate": 0.98
            },
            {
                "id": "redis_conn_001",
                "category": "cache",
                "title": "Redis Connection Issues",
                "problem": "Redis connection failures affecting application performance",
                "solution": "Check Redis service status, verify network connectivity, adjust timeouts",
                "steps": [
                    "Check Redis service: sudo systemctl status redis",
                    "Test connectivity: redis-cli ping",
                    "Verify Redis configuration in application",
                    "Check network connectivity and firewall rules",
                    "Increase connection timeout values"
                ],
                "technologies": ["redis", "cache", "session"],
                "error_patterns": ["redis connection", "connection timeout", "redis server"],
                "severity": "high",
                "resolution_time": "20-40 minutes",
                "success_rate": 0.90
            },
            {
                "id": "auth_fail_001",
                "category": "authentication",
                "title": "Authentication Failure Investigation",
                "problem": "Multiple authentication failures indicating potential security issues",
                "solution": "Review credentials, check for brute force attacks, update security policies",
                "steps": [
                    "Review authentication logs for patterns",
                    "Check for brute force attacks: fail2ban status",
                    "Verify user credentials and permissions",
                    "Implement rate limiting for login attempts",
                    "Enable multi-factor authentication"
                ],
                "technologies": ["ldap", "oauth", "saml", "authentication"],
                "error_patterns": ["authentication failed", "invalid credentials", "login failed"],
                "severity": "high",
                "resolution_time": "30-60 minutes",
                "success_rate": 0.85
            }
        ]
        
        # Convert to documents and metadata
        for item in default_knowledge:
            document_text = f"{item['title']} {item['problem']} {item['solution']} {' '.join(item['steps'])} {' '.join(item['error_patterns'])}"
            self.documents.append(document_text)
            self.metadata.append(item)
    
    def _build_embeddings(self):
        """Build TF-IDF embeddings for the knowledge base documents."""
        if not self.documents:
            return
        
        # Create TF-IDF matrix
        tfidf_matrix = self.vectorizer.fit_transform(self.documents)
        
        # Apply dimensionality reduction
        self.document_embeddings = self.svd.fit_transform(tfidf_matrix)
        
        print(f"✅ Built embeddings for {len(self.documents)} documents with {self.embedding_dim} dimensions")
    
    def _save_knowledge_base(self):
        """Save knowledge base and embeddings to disk."""
        # Save knowledge base
        kb_data = {
            "documents": self.documents,
            "metadata": self.metadata,
            "last_updated": datetime.now().isoformat()
        }
        
        kb_path = os.path.join(self.knowledge_base_dir, "knowledge_base.json")
        with open(kb_path, 'w') as f:
            json.dump(kb_data, f, indent=2)
        
        # Save embeddings and models
        embeddings_data = {
            "document_embeddings": self.document_embeddings,
            "vectorizer": self.vectorizer,
            "svd": self.svd
        }
        
        embeddings_path = os.path.join(self.knowledge_base_dir, "embeddings.pkl")
        with open(embeddings_path, 'wb') as f:
            pickle.dump(embeddings_data, f)
        
        print(f"💾 Saved knowledge base with {len(self.documents)} documents")
    
    def _load_knowledge_base(self):
        """Load knowledge base and embeddings from disk."""
        # Load knowledge base
        kb_path = os.path.join(self.knowledge_base_dir, "knowledge_base.json")
        with open(kb_path, 'r') as f:
            kb_data = json.load(f)
        
        self.documents = kb_data["documents"]
        self.metadata = kb_data["metadata"]
        
        # Load embeddings and models
        embeddings_path = os.path.join(self.knowledge_base_dir, "embeddings.pkl")
        with open(embeddings_path, 'rb') as f:
            embeddings_data = pickle.load(f)
        
        self.document_embeddings = embeddings_data["document_embeddings"]
        self.vectorizer = embeddings_data["vectorizer"]
        self.svd = embeddings_data["svd"]
        
        print(f"📂 Loaded knowledge base with {len(self.documents)} documents")
    
    def add_knowledge(self, title: str, problem: str, solution: str, steps: List[str], 
                     category: str, technologies: List[str], error_patterns: List[str],
                     severity: str = "medium", resolution_time: str = "unknown", 
                     success_rate: float = 0.8):
        """Add new knowledge to the RAG system."""
        # Create document ID
        doc_id = hashlib.md5(f"{title}{problem}".encode()).hexdigest()[:12]
        
        # Create document text
        document_text = f"{title} {problem} {solution} {' '.join(steps)} {' '.join(error_patterns)}"
        
        # Create metadata
        metadata = {
            "id": doc_id,
            "category": category,
            "title": title,
            "problem": problem,
            "solution": solution,
            "steps": steps,
            "technologies": technologies,
            "error_patterns": error_patterns,
            "severity": severity,
            "resolution_time": resolution_time,
            "success_rate": success_rate,
            "created_at": datetime.now().isoformat()
        }
        
        # Add to collections
        self.documents.append(document_text)
        self.metadata.append(metadata)
        
        # Rebuild embeddings
        self._build_embeddings()
        self._save_knowledge_base()
        
        print(f"✅ Added new knowledge: {title}")
    
    def retrieve_relevant_knowledge(self, query: str, error_categories: List[str] = None, 
                                  top_k: int = 5) -> List[Dict[str, Any]]:
        """Retrieve relevant knowledge based on query and error categories."""
        if not self.documents or self.document_embeddings is None:
            return []
        
        # Transform query to same embedding space
        query_tfidf = self.vectorizer.transform([query])
        query_embedding = self.svd.transform(query_tfidf)
        
        # Calculate cosine similarities
        similarities = cosine_similarity(query_embedding, self.document_embeddings)[0]
        
        # Get top-k most similar documents
        top_indices = similarities.argsort()[-top_k*2:][::-1]  # Get more candidates for filtering
        
        relevant_docs = []
        for idx in top_indices:
            if len(relevant_docs) >= top_k:
                break
                
            doc_metadata = self.metadata[idx].copy()
            doc_metadata['similarity_score'] = float(similarities[idx])
            
            # Filter by category if specified
            if error_categories:
                doc_category = doc_metadata.get('category', '').lower()
                doc_technologies = [tech.lower() for tech in doc_metadata.get('technologies', [])]
                
                category_match = any(cat.lower() in doc_category or 
                                   cat.lower() in doc_technologies 
                                   for cat in error_categories)
                
                if category_match or similarities[idx] > 0.3:  # Include high similarity regardless
                    relevant_docs.append(doc_metadata)
            else:
                if similarities[idx] > 0.1:  # Minimum similarity threshold
                    relevant_docs.append(doc_metadata)
        
        # Sort by similarity score
        relevant_docs.sort(key=lambda x: x['similarity_score'], reverse=True)
        
        return relevant_docs[:top_k]
    
    def generate_rag_enhanced_recommendations(self, error_context: str, 
                                            error_categories: List[str],
                                            llm_processor) -> List[Dict[str, Any]]:
        """Generate recommendations enhanced with RAG retrieval."""
        # Retrieve relevant knowledge
        relevant_knowledge = self.retrieve_relevant_knowledge(
            query=error_context, 
            error_categories=error_categories,
            top_k=3
        )
        
        if not relevant_knowledge:
            return []
        
        # Prepare context with retrieved knowledge
        rag_context = self._prepare_rag_context(error_context, relevant_knowledge)
        
        # Generate enhanced recommendations using LLM
        return self._generate_llm_recommendations_with_rag(llm_processor, rag_context, relevant_knowledge)
    
    def _prepare_rag_context(self, error_context: str, relevant_knowledge: List[Dict]) -> str:
        """Prepare context combining error analysis with retrieved knowledge."""
        context_parts = [
            "CURRENT LOG ANALYSIS:",
            error_context,
            "",
            "RELEVANT HISTORICAL SOLUTIONS:"
        ]
        
        for i, knowledge in enumerate(relevant_knowledge, 1):
            context_parts.extend([
                f"\n{i}. {knowledge['title']} (Similarity: {knowledge['similarity_score']:.2f})",
                f"   Problem: {knowledge['problem']}",
                f"   Solution: {knowledge['solution']}",
                f"   Category: {knowledge['category']} | Severity: {knowledge['severity']}",
                f"   Success Rate: {knowledge['success_rate']*100:.0f}% | Time: {knowledge['resolution_time']}",
                f"   Technologies: {', '.join(knowledge['technologies'])}",
                ""
            ])
        
        return '\n'.join(context_parts)
    
    def _generate_llm_recommendations_with_rag(self, llm_processor, rag_context: str, 
                                             relevant_knowledge: List[Dict]) -> List[Dict[str, Any]]:
        """Generate LLM recommendations enhanced with RAG context."""
        try:
            import json
            
            prompt = f"""You are a senior DevOps engineer with access to a knowledge base of proven solutions. Based on the current log analysis and relevant historical solutions provided, generate 3-5 specific, actionable recommendations.

{rag_context}

INSTRUCTIONS:
1. Prioritize solutions that have been proven successful (high success rates)
2. Adapt the historical solutions to the current context
3. Provide specific, actionable steps with commands where possible
4. Consider the technologies and error patterns from both current and historical data
5. Estimate implementation effort and priority based on proven resolution times

For each recommendation, provide:
- title: Clear, specific title (max 50 chars)
- description: Brief summary explaining what needs to be done and why
- steps: Array of detailed, actionable steps with specific commands
- priority: CRITICAL, HIGH, MEDIUM, LOW (based on severity and success rate)
- effort: QUICK, MODERATE, EXTENSIVE (based on historical resolution times)
- category: IMMEDIATE, MONITORING, INFRASTRUCTURE, CODE, SECURITY
- reasoning: Why this solution is recommended based on historical success
- confidence: HIGH, MEDIUM, LOW (based on similarity to historical cases)

Format as JSON array with these exact keys.

Example:
[
  {{
    "title": "Fix Database Connection Pool",
    "description": "Database connection pool exhausted. Historical solution shows 95% success rate.",
    "steps": [
      "Check current pool: SELECT count(*) FROM pg_stat_activity",
      "Edit /etc/postgresql/postgresql.conf, set max_connections = 200",
      "Restart PostgreSQL: sudo systemctl restart postgresql",
      "Monitor for 30 minutes and verify connection stability"
    ],
    "priority": "HIGH",
    "effort": "QUICK",
    "category": "IMMEDIATE",
    "reasoning": "This exact issue resolved in 95% of cases within 30-60 minutes based on knowledge base",
    "confidence": "HIGH"
  }}
]"""

            response = llm_processor.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are an expert DevOps engineer with access to proven historical solutions. Always respond with valid JSON and prioritize high-confidence solutions."},
                    {"role": "user", "content": prompt}
                ],
                user=json.dumps({"appkey": llm_processor.appkey}),
                temperature=0.5,  # Lower temperature for more consistent RAG responses
                max_tokens=2500
            )
            
            response_text = response.choices[0].message.content.strip()
            
            # Extract JSON from response
            import re
            json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
            if json_match:
                recommendations_json = json.loads(json_match.group())
                
                # Format recommendations with RAG enhancements
                formatted_recommendations = []
                for i, rec in enumerate(recommendations_json):
                    if isinstance(rec, dict) and 'title' in rec:
                        # Find the most relevant knowledge for this recommendation
                        most_relevant = self._find_most_relevant_knowledge(rec, relevant_knowledge)
                        
                        formatted_rec = {
                            'problem': rec.get('title', f'RAG Recommendation {i+1}'),
                            'description': rec.get('description', 'RAG-enhanced recommendation'),
                            'steps': rec.get('steps', []),
                            'priority': rec.get('priority', 'MEDIUM'),
                            'effort': rec.get('effort', 'MODERATE'),
                            'category': rec.get('category', 'GENERAL'),
                            'reasoning': rec.get('reasoning', 'Based on historical knowledge base'),
                            'confidence': rec.get('confidence', 'MEDIUM'),
                            'source': 'rag_enhanced',
                            'severity': self._map_priority_to_severity(rec.get('priority', 'MEDIUM')),
                            'knowledge_base_match': most_relevant,
                            'historical_success_rate': most_relevant.get('success_rate', 0.8) if most_relevant else 0.8
                        }
                        formatted_recommendations.append(formatted_rec)
                
                return formatted_recommendations[:5]
            
        except Exception as e:
            print(f"RAG-enhanced recommendation generation error: {e}")
        
        return []
    
    def _find_most_relevant_knowledge(self, recommendation: Dict, knowledge_list: List[Dict]) -> Optional[Dict]:
        """Find the most relevant knowledge item for a recommendation."""
        rec_text = (recommendation.get('title', '') + ' ' + recommendation.get('description', '')).lower()
        
        best_match = None
        best_score = 0
        
        for knowledge in knowledge_list:
            # Calculate relevance score based on multiple factors
            score = 0
            
            # Title similarity
            if any(word in rec_text for word in knowledge['title'].lower().split()):
                score += 0.3
            
            # Category match
            if knowledge['category'].lower() in rec_text:
                score += 0.2
            
            # Technology match
            for tech in knowledge['technologies']:
                if tech.lower() in rec_text:
                    score += 0.1
            
            # Error pattern match
            for pattern in knowledge['error_patterns']:
                if pattern.lower() in rec_text:
                    score += 0.2
            
            # Use similarity score if available
            if 'similarity_score' in knowledge:
                score += knowledge['similarity_score'] * 0.2
            
            if score > best_score:
                best_score = score
                best_match = knowledge
        
        return best_match
    
    def _map_priority_to_severity(self, priority: str) -> str:
        """Map priority to severity level."""
        mapping = {
            'CRITICAL': 'critical',
            'HIGH': 'high',
            'MEDIUM': 'medium', 
            'LOW': 'low'
        }
        return mapping.get(priority.upper(), 'medium')
    
    def get_knowledge_base_stats(self) -> Dict[str, Any]:
        """Get statistics about the knowledge base."""
        if not self.metadata:
            return {}
        
        categories = {}
        technologies = set()
        avg_success_rate = 0
        
        for item in self.metadata:
            category = item.get('category', 'unknown')
            categories[category] = categories.get(category, 0) + 1
            
            for tech in item.get('technologies', []):
                technologies.add(tech)
            
            avg_success_rate += item.get('success_rate', 0.8)
        
        avg_success_rate /= len(self.metadata)
        
        return {
            'total_documents': len(self.documents),
            'categories': categories,
            'technologies': list(technologies),
            'average_success_rate': avg_success_rate,
            'embedding_dimensions': self.embedding_dim
        }
