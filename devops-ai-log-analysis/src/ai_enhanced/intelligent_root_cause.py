#!/usr/bin/env python3
"""
Intelligent Root Cause Analysis Module
Analyzes errors and anomalies using the DevOps knowledge base to provide recommendations.
"""

import re
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from collections import defaultdict, Counter
from dataclasses import dataclass, asdict

from .knowledge_base import DevOpsKnowledgeBase, knowledge_base

@dataclass
class AnalysisResult:
    """Result of root cause analysis."""
    error_id: str
    timestamp: str
    error_message: str
    category: str
    severity: str
    confidence_score: float
    recommendations: List[Dict[str, Any]]
    related_patterns: List[str]
    suggested_actions: List[str]
    prevention_tips: List[str]
    diagnostic_commands: List[str]
    estimated_resolution_time: str

class IntelligentRootCauseAnalyzer:
    """Intelligent root cause analyzer with knowledge base integration."""
    
    def __init__(self, knowledge_base: DevOpsKnowledgeBase = None):
        self.kb = knowledge_base or knowledge_base
        self.analysis_history = []
        self.pattern_cache = {}
        
        # Common error patterns
        self.error_patterns = {
            'database': [
                r'connection\s+timeout',
                r'deadlock\s+detected',
                r'table\s+.*\s+doesn\'t\s+exist',
                r'access\s+denied',
                r'too\s+many\s+connections',
                r'lock\s+wait\s+timeout'
            ],
            'build': [
                r'compilation\s+error',
                r'build\s+failed',
                r'dependency\s+not\s+found',
                r'test\s+failed',
                r'maven\s+build\s+failure',
                r'gradle\s+build\s+failed'
            ],
            'infrastructure': [
                r'out\s+of\s+memory',
                r'disk\s+full',
                r'no\s+space\s+left',
                r'cpu\s+usage\s+high',
                r'load\s+average\s+high',
                r'permission\s+denied'
            ],
            'network': [
                r'connection\s+refused',
                r'network\s+timeout',
                r'dns\s+resolution\s+failed',
                r'ssl\s+handshake\s+failed',
                r'certificate\s+expired'
            ],
            'security': [
                r'authentication\s+failed',
                r'unauthorized\s+access',
                r'invalid\s+credentials',
                r'token\s+expired',
                r'access\s+denied'
            ],
            'container': [
                r'container\s+exited',
                r'image\s+not\s+found',
                r'port\s+already\s+in\s+use',
                r'volume\s+mount\s+failed',
                r'crashloopbackoff'
            ]
        }
    
    def analyze_error(self, error_message: str, 
                     context: Dict[str, Any] = None) -> AnalysisResult:
        """Analyze an error message and provide root cause analysis."""
        
        # Generate unique error ID
        error_id = f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(error_message) % 10000}"
        
        # Detect category
        category = self._detect_category(error_message)
        
        # Assess severity
        severity = self._assess_severity(error_message)
        
        # Get recommendations from knowledge base
        recommendations = self.kb.get_recommendations(error_message, category)
        
        # Calculate confidence score
        confidence_score = self._calculate_confidence(error_message, recommendations)
        
        # Extract patterns
        related_patterns = self._extract_patterns(error_message)
        
        # Generate suggested actions
        suggested_actions = self._generate_suggested_actions(error_message, recommendations)
        
        # Generate prevention tips
        prevention_tips = self._generate_prevention_tips(recommendations)
        
        # Generate diagnostic commands
        diagnostic_commands = self._generate_diagnostic_commands(category, error_message)
        
        # Estimate resolution time
        estimated_time = self._estimate_resolution_time(severity, category)
        
        # Create analysis result
        result = AnalysisResult(
            error_id=error_id,
            timestamp=datetime.now().isoformat(),
            error_message=error_message,
            category=category,
            severity=severity,
            confidence_score=confidence_score,
            recommendations=recommendations,
            related_patterns=related_patterns,
            suggested_actions=suggested_actions,
            prevention_tips=prevention_tips,
            diagnostic_commands=diagnostic_commands,
            estimated_resolution_time=estimated_time
        )
        
        # Store in analysis history
        self.analysis_history.append(result)
        
        return result
    
    def _detect_category(self, error_message: str) -> str:
        """Detect the category of the error based on patterns."""
        error_lower = error_message.lower()
        
        category_scores = defaultdict(int)
        
        for category, patterns in self.error_patterns.items():
            for pattern in patterns:
                if re.search(pattern, error_lower):
                    category_scores[category] += 1
        
        if category_scores:
            return max(category_scores, key=category_scores.get)
        
        return "General"
    
    def _assess_severity(self, error_message: str) -> str:
        """Assess the severity of the error."""
        error_lower = error_message.lower()
        
        critical_indicators = [
            'critical', 'fatal', 'emergency', 'panic', 'system down',
            'out of memory', 'disk full', 'database down', 'service unavailable'
        ]
        
        high_indicators = [
            'error', 'failed', 'exception', 'timeout', 'deadlock',
            'connection refused', 'access denied', 'build failed'
        ]
        
        medium_indicators = [
            'warning', 'deprecated', 'slow', 'performance',
            'retry', 'fallback'
        ]
        
        for indicator in critical_indicators:
            if indicator in error_lower:
                return "critical"
        
        for indicator in high_indicators:
            if indicator in error_lower:
                return "high"
        
        for indicator in medium_indicators:
            if indicator in error_lower:
                return "medium"
        
        return "low"
    
    def _calculate_confidence(self, error_message: str, 
                            recommendations: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for the analysis."""
        if not recommendations:
            return 0.1
        
        # Base confidence on number of recommendations and their relevance
        base_confidence = min(len(recommendations) * 0.2, 0.8)
        
        # Adjust based on relevance scores
        if recommendations:
            avg_relevance = sum(r.get('relevance_score', 0) for r in recommendations) / len(recommendations)
            base_confidence += min(avg_relevance * 0.1, 0.2)
        
        return min(base_confidence, 1.0)
    
    def _extract_patterns(self, error_message: str) -> List[str]:
        """Extract error patterns from the message."""
        patterns = []
        error_lower = error_message.lower()
        
        # Common patterns
        common_patterns = [
            r'(\w+)\s+error',
            r'(\w+)\s+failed',
            r'(\w+)\s+timeout',
            r'(\w+)\s+not\s+found',
            r'(\w+)\s+denied',
            r'(\w+)\s+exception'
        ]
        
        for pattern in common_patterns:
            matches = re.findall(pattern, error_lower)
            for match in matches:
                patterns.append(f"{match} {pattern.split('(')[1].split(')')[0]}")
        
        return patterns[:5]  # Return top 5 patterns
    
    def _generate_suggested_actions(self, error_message: str, 
                                  recommendations: List[Dict[str, Any]]) -> List[str]:
        """Generate immediate suggested actions."""
        actions = []
        
        # Add actions from recommendations
        for rec in recommendations[:3]:  # Top 3 recommendations
            actions.extend(rec.get('solutions', [])[:2])  # First 2 solutions each
        
        # Add generic actions based on error type
        error_lower = error_message.lower()
        
        if 'timeout' in error_lower:
            actions.append("Check network connectivity and increase timeout values")
        
        if 'connection' in error_lower:
            actions.append("Verify service availability and network configuration")
        
        if 'memory' in error_lower:
            actions.append("Check memory usage and consider increasing available RAM")
        
        if 'disk' in error_lower:
            actions.append("Clean up disk space and check for large log files")
        
        # Remove duplicates and limit to top 5
        unique_actions = list(dict.fromkeys(actions))
        return unique_actions[:5]
    
    def _generate_prevention_tips(self, recommendations: List[Dict[str, Any]]) -> List[str]:
        """Generate prevention tips based on recommendations."""
        tips = []
        
        for rec in recommendations[:3]:
            tips.extend(rec.get('prevention', []))
        
        # Remove duplicates and limit to top 5
        unique_tips = list(dict.fromkeys(tips))
        return unique_tips[:5]
    
    def _generate_diagnostic_commands(self, category: str, error_message: str) -> List[str]:
        """Generate diagnostic commands based on category and error."""
        commands = []
        
        if category.lower() == 'database':
            commands.extend([
                "SHOW PROCESSLIST;",
                "SHOW ENGINE INNODB STATUS;",
                "SELECT * FROM information_schema.INNODB_LOCKS;",
                "SHOW VARIABLES LIKE 'max_connections';",
                "mysqladmin processlist"
            ])
        
        elif category.lower() == 'infrastructure':
            commands.extend([
                "top -n 1",
                "df -h",
                "free -h",
                "iostat -x 1 5",
                "netstat -tuln",
                "ps aux --sort=-%mem | head -10"
            ])
        
        elif category.lower() == 'network':
            commands.extend([
                "ping -c 4 <target_host>",
                "telnet <host> <port>",
                "nslookup <hostname>",
                "traceroute <target_host>",
                "netstat -an | grep <port>"
            ])
        
        elif category.lower() == 'container':
            commands.extend([
                "docker ps -a",
                "docker logs <container_id>",
                "kubectl get pods",
                "kubectl describe pod <pod_name>",
                "docker inspect <container_id>"
            ])
        
        elif category.lower() == 'build':
            commands.extend([
                "mvn clean compile",
                "gradle build --info",
                "npm install --verbose",
                "pip install -r requirements.txt",
                "docker build --no-cache ."
            ])
        
        else:
            commands.extend([
                "tail -f /var/log/syslog",
                "journalctl -xe",
                "systemctl status <service_name>",
                "ps aux | grep <process_name>"
            ])
        
        return commands[:5]
    
    def _estimate_resolution_time(self, severity: str, category: str) -> str:
        """Estimate resolution time based on severity and category."""
        time_estimates = {
            'critical': {
                'database': '1-2 hours',
                'infrastructure': '30 minutes - 1 hour',
                'network': '1-3 hours',
                'security': '2-4 hours',
                'container': '30 minutes - 1 hour'
            },
            'high': {
                'database': '2-4 hours',
                'infrastructure': '1-2 hours',
                'network': '1-2 hours',
                'security': '2-6 hours',
                'container': '1-2 hours'
            },
            'medium': {
                'database': '4-8 hours',
                'infrastructure': '2-4 hours',
                'network': '2-4 hours',
                'security': '4-8 hours',
                'container': '2-4 hours'
            },
            'low': {
                'database': '1-2 days',
                'infrastructure': '4-8 hours',
                'network': '4-8 hours',
                'security': '1-2 days',
                'container': '4-8 hours'
            }
        }
        
        return time_estimates.get(severity, {}).get(category.lower(), '2-4 hours')
    
    def analyze_multiple_errors(self, error_messages: List[str]) -> Dict[str, Any]:
        """Analyze multiple errors and find common patterns."""
        results = []
        
        for error_msg in error_messages:
            result = self.analyze_error(error_msg)
            results.append(result)
        
        # Find common patterns
        all_categories = [r.category for r in results]
        all_severities = [r.severity for r in results]
        
        category_counts = Counter(all_categories)
        severity_counts = Counter(all_severities)
        
        # Generate summary
        summary = {
            'total_errors': len(error_messages),
            'analysis_results': [asdict(r) for r in results],
            'common_categories': dict(category_counts.most_common(5)),
            'severity_distribution': dict(severity_counts),
            'overall_confidence': sum(r.confidence_score for r in results) / len(results),
            'recommended_priority': self._determine_priority(results)
        }
        
        return summary
    
    def _determine_priority(self, results: List[AnalysisResult]) -> str:
        """Determine overall priority based on analysis results."""
        critical_count = sum(1 for r in results if r.severity == 'critical')
        high_count = sum(1 for r in results if r.severity == 'high')
        
        if critical_count > 0:
            return "Critical - Immediate attention required"
        elif high_count > len(results) * 0.5:
            return "High - Address within 2 hours"
        else:
            return "Medium - Address within 24 hours"
    
    def get_trending_issues(self, days: int = 7) -> Dict[str, Any]:
        """Get trending issues from analysis history."""
        recent_analyses = [
            a for a in self.analysis_history 
            if (datetime.now() - datetime.fromisoformat(a.timestamp)).days <= days
        ]
        
        if not recent_analyses:
            return {"message": "No recent analysis data available"}
        
        categories = [a.category for a in recent_analyses]
        severities = [a.severity for a in recent_analyses]
        
        return {
            'total_analyses': len(recent_analyses),
            'trending_categories': dict(Counter(categories).most_common(5)),
            'severity_trends': dict(Counter(severities)),
            'avg_confidence': sum(a.confidence_score for a in recent_analyses) / len(recent_analyses),
            'most_common_recommendations': self._get_common_recommendations(recent_analyses)
        }
    
    def _get_common_recommendations(self, analyses: List[AnalysisResult]) -> List[str]:
        """Get most common recommendations from analyses."""
        all_recommendations = []
        
        for analysis in analyses:
            for rec in analysis.recommendations:
                all_recommendations.extend(rec.get('solutions', []))
        
        rec_counts = Counter(all_recommendations)
        return [rec for rec, count in rec_counts.most_common(5)]
    
    def export_analysis_history(self, filename: str):
        """Export analysis history to JSON file."""
        data = {
            'analyses': [asdict(a) for a in self.analysis_history],
            'export_date': datetime.now().isoformat(),
            'total_count': len(self.analysis_history)
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
    
    def clear_analysis_history(self):
        """Clear analysis history."""
        self.analysis_history.clear()
    
    def get_analysis_stats(self) -> Dict[str, Any]:
        """Get statistics about performed analyses."""
        if not self.analysis_history:
            return {"message": "No analysis history available"}
        
        categories = [a.category for a in self.analysis_history]
        severities = [a.severity for a in self.analysis_history]
        
        return {
            'total_analyses': len(self.analysis_history),
            'category_distribution': dict(Counter(categories)),
            'severity_distribution': dict(Counter(severities)),
            'average_confidence': sum(a.confidence_score for a in self.analysis_history) / len(self.analysis_history),
            'knowledge_base_stats': self.kb.get_statistics()
        }

# Global analyzer instance
root_cause_analyzer = IntelligentRootCauseAnalyzer()
