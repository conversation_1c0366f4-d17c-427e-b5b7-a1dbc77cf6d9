from flask import Blueprint, request, jsonify
import json
import os

# Define a Flask blueprint
knowledge_base_blueprint = Blueprint('knowledge_base', __name__)

KNOWLEDGE_BASE_PATH = "data/processed/knowledge_base.json"

# Load the knowledge base
def load_knowledge_base():
    if os.path.exists(KNOWLEDGE_BASE_PATH):
        with open(KNOWLEDGE_BASE_PATH, "r") as file:
            return json.load(file)
    return []

# Save the knowledge base
def save_knowledge_base(knowledge_base):
    with open(KNOWLEDGE_BASE_PATH, "w") as file:
        json.dump(knowledge_base, file, indent=4)

@knowledge_base_blueprint.route("/api/knowledge_base", methods=["GET"])
def get_knowledge_base():
    """Retrieve the current knowledge base."""
    knowledge_base = load_knowledge_base()
    return jsonify(knowledge_base)

@knowledge_base_blueprint.route("/api/knowledge_base", methods=["POST"])
def add_to_knowledge_base():
    """Add a new pattern and solution to the knowledge base."""
    data = request.json
    if not data or "pattern" not in data or "solution" not in data:
        return jsonify({"error": "Invalid input. 'pattern' and 'solution' are required."}), 400

    knowledge_base = load_knowledge_base()
    knowledge_base.append({
        "pattern": data["pattern"],
        "solution": data["solution"],
        "metadata": data.get("metadata", {})
    })
    save_knowledge_base(knowledge_base)

    return jsonify({"message": "Entry added successfully."}), 201

@knowledge_base_blueprint.route("/api/historical_data", methods=["GET"])
def get_historical_data():
    """Retrieve historical logs and analysis results."""
    historical_data_path = "data/processed/historical_logs.json"
    if os.path.exists(historical_data_path):
        with open(historical_data_path, "r") as file:
            historical_data = json.load(file)
        return jsonify(historical_data)
    return jsonify({"error": "No historical data found."}), 404
