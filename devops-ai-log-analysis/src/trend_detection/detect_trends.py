from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

class TrendDetector:
    def __init__(self, data):
        self.data = data

    def preprocess_data(self):
        # Convert timestamps to datetime objects
        self.data['timestamp'] = pd.to_datetime(self.data['timestamp'])
        self.data.set_index('timestamp', inplace=True)

    def detect_trends(self):
        self.preprocess_data()
        trend = self.data.rolling(window='7D').mean()  # Example: 7-day rolling mean
        return trend

    def visualize_trends(self, trend):
        plt.figure(figsize=(12, 6))
        plt.plot(trend.index, trend, label='Trend', color='blue')
        plt.title('Detected Trends Over Time')
        plt.xlabel('Date')
        plt.ylabel('Value')
        plt.legend()
        plt.grid()
        plt.show()

    def generate_report(self, trend):
        report = trend.describe()
        return report

def detect_trends(timeseries_data):
    """
    Detect trends in time series data.
    
    Args:
        timeseries_data: pandas DataFrame with time series data
        
    Returns:
        dict: Trend analysis results
    """
    if timeseries_data is None or timeseries_data.empty:
        return {'trends': {}, 'summary': 'No time series data to analyze'}
    
    trends = {}
    
    # Analyze numerical columns for trends
    numerical_columns = timeseries_data.select_dtypes(include=[np.number]).columns
    
    for column in numerical_columns:
        if column in timeseries_data.columns:
            values = timeseries_data[column].values
            
            # Simple trend detection using linear regression slope
            if len(values) > 1:
                x = np.arange(len(values))
                slope, intercept = np.polyfit(x, values, 1)
                
                # Determine trend direction
                if slope > 0.1:
                    trend_direction = 'increasing'
                elif slope < -0.1:
                    trend_direction = 'decreasing'
                else:
                    trend_direction = 'stable'
                
                trends[column] = {
                    'direction': trend_direction,
                    'slope': slope,
                    'mean_value': np.mean(values),
                    'std_value': np.std(values),
                    'max_value': np.max(values),
                    'min_value': np.min(values)
                }
    
    return {
        'trends': trends,
        'summary': f'Analyzed trends for {len(trends)} metrics over {len(timeseries_data)} time points'
    }