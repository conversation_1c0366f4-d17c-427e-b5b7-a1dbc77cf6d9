def preprocess_json(log_data):
    """
    Enhanced JSON log preprocessing supporting various JSON formats and field mappings.
    
    Args:
        log_data: List of dictionaries from JSON ingestion
        
    Returns:
        list: Processed log entries with standardized fields
    """
    import json
    from datetime import datetime
    import re

    # Handle case where log_data is already a list of dictionaries
    if isinstance(log_data, list):
        logs = log_data
    else:
        # Load JSON data if it's a string
        try:
            logs = json.loads(log_data)
        except json.JSONDecodeError as e:
            raise ValueError("Invalid JSON data") from e

    # Process and normalize each log entry
    processed_logs = []
    for log in logs:
        if not isinstance(log, dict):
            continue
            
        processed_log = {
            "timestamp": None,
            "level": "INFO",  # Default level
            "message": "",
            "service": None,
            "error_type": None,
            "severity": None,
            "details": None,
            "original": log  # Keep original for debugging
        }
        
        # Extract and normalize timestamp
        timestamp_fields = ['timestamp', 'time', 'datetime', '@timestamp', 'ts']
        for field in timestamp_fields:
            if field in log and log[field]:
                processed_log['timestamp'] = normalize_timestamp(log[field])
                break
        
        # Extract and normalize log level
        level_fields = ['level', 'severity', 'log_level', 'priority']
        for field in level_fields:
            if field in log and log[field]:
                level = str(log[field]).upper()
                # Map various severity levels to standard levels
                if level in ['CRITICAL', 'FATAL', 'CRIT']:
                    processed_log['level'] = 'CRITICAL'
                elif level in ['ERROR', 'ERR', 'HIGH']:
                    processed_log['level'] = 'ERROR'
                elif level in ['WARNING', 'WARN', 'MEDIUM']:
                    processed_log['level'] = 'WARNING'
                elif level in ['INFO', 'INFORMATION', 'LOW']:
                    processed_log['level'] = 'INFO'
                elif level in ['DEBUG', 'TRACE']:
                    processed_log['level'] = 'DEBUG'
                else:
                    processed_log['level'] = level
                break
        
        # Extract message with fallback logic
        message_fields = ['message', 'msg', 'error', 'description', 'text', 'content']
        for field in message_fields:
            if field in log and log[field]:
                processed_log['message'] = str(log[field])
                break
        
        # If still no message, construct from other fields
        if not processed_log['message']:
            if 'type' in log:
                processed_log['message'] = f"{log.get('type', '')}: {log.get('details', '')}"
            else:
                # Extract key-value pairs that look like error information
                message_parts = []
                for key, value in log.items():
                    if key not in ['timestamp', 'time', 'datetime', '@timestamp', 'ts'] and value:
                        message_parts.append(f"{key}: {value}")
                processed_log['message'] = " | ".join(message_parts) if message_parts else str(log)
        
        # Extract service/component
        service_fields = ['service', 'component', 'module', 'source', 'logger', 'host', 'hostname']
        for field in service_fields:
            if field in log and log[field]:
                processed_log['service'] = str(log[field])
                break
        
        # Extract error type/category
        type_fields = ['type', 'error_type', 'category', 'class', 'kind']
        for field in type_fields:
            if field in log and log[field]:
                processed_log['error_type'] = str(log[field])
                break
        
        # Extract additional details
        details_fields = ['details', 'detail', 'stack_trace', 'trace', 'context', 'exception']
        for field in details_fields:
            if field in log and log[field]:
                processed_log['details'] = str(log[field])
                break
        
        # Extract severity if different from level
        if 'severity' in log and log['severity']:
            processed_log['severity'] = str(log['severity'])
        
        processed_logs.append(processed_log)

    return processed_logs

def normalize_timestamp(timestamp_str):
    """
    Normalize various timestamp formats to a standard datetime object.
    
    Args:
        timestamp_str: Timestamp string in various formats
        
    Returns:
        datetime object or original string if parsing fails
    """
    if not timestamp_str:
        return None
        
    timestamp_formats = [
        "%Y-%m-%dT%H:%M:%S.%fZ",  # ISO format with microseconds
        "%Y-%m-%dT%H:%M:%SZ",     # ISO format without microseconds
        "%Y-%m-%dT%H:%M:%S",      # ISO format without timezone
        "%Y-%m-%d %H:%M:%S.%f",   # Standard format with microseconds
        "%Y-%m-%d %H:%M:%S",      # Standard format
        "%d/%m/%Y %H:%M:%S",      # European format
        "%m/%d/%Y %H:%M:%S",      # US format
    ]
    
    for fmt in timestamp_formats:
        try:
            from datetime import datetime
            return datetime.strptime(str(timestamp_str), fmt)
        except ValueError:
            continue
    
    # If all parsing fails, return the original string
    return str(timestamp_str)

def normalize_timestamps(processed_logs):
    """
    Legacy function for backward compatibility.
    """
    return processed_logs