from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords
import string

def preprocess_text(logs):
    """
    Preprocess text logs - can handle both single string and list of strings
    
    Args:
        logs: Either a single log string or list of log strings
        
    Returns:
        list: Processed log entries
    """
    if isinstance(logs, str):
        logs = [logs]
    
    processed_logs = []
    for log in logs:
        # Basic preprocessing - clean and normalize
        # Remove extra whitespace
        log = ' '.join(log.split())
        # Keep the log mostly intact for downstream processing
        processed_logs.append(log)
    
    return processed_logs

def preprocess_logs(logs):
    processed_logs = []
    for log in logs:
        processed_log = preprocess_text(log)
        processed_logs.append(processed_log)
    return processed_logs