import xml.etree.ElementTree as ET
import os

def ingest_xml_logs(file_path):
    """
    Ingests XML logs from the specified file path and returns a list of parsed log entries.
    
    Args:
        file_path (str): The path to the XML log file.
        
    Returns:
        list: A list of dictionaries containing parsed log entries.
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"The file {file_path} does not exist.")
    
    log_entries = []
    
    tree = ET.parse(file_path)
    root = tree.getroot()
    
    for log in root.findall('log'):
        entry = {}
        for child in log:
            entry[child.tag] = child.text
        log_entries.append(entry)
    
    return log_entries

def ingest_multiple_xml_logs(directory_path):
    """
    Ingests all XML log files from the specified directory and returns a combined list of parsed log entries.
    
    Args:
        directory_path (str): The path to the directory containing XML log files.
        
    Returns:
        list: A list of dictionaries containing parsed log entries from all files.
    """
    all_log_entries = []
    
    for filename in os.listdir(directory_path):
        if filename.endswith('.xml'):
            file_path = os.path.join(directory_path, filename)
            log_entries = ingest_xml_logs(file_path)
            all_log_entries.extend(log_entries)
    
    return all_log_entries