def ingest_text_logs(file_path, chunk_size=1000, detect_multiline=True):
    """
    Ingests text logs from the specified file path with support for large files and multiline logs.
    
    Args:
        file_path (str): The path to the text log file.
        chunk_size (int): Number of lines to process at once for memory efficiency.
        detect_multiline (bool): Whether to detect and group multiline log entries.
        
    Returns:
        list: A list of structured log entries (including multiline entries).
    """
    import re
    from datetime import datetime
    
    logs = []
    current_multiline_entry = None
    
    # Pattern to detect log line start (timestamp + level)
    log_start_pattern = re.compile(r'^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\s+(INFO|WARN|ERROR|DEBUG|TRACE)')
    
    def process_chunk(lines):
        nonlocal current_multiline_entry
        processed_logs = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if detect_multiline and log_start_pattern.match(line):
                # This is a new log entry
                if current_multiline_entry:
                    processed_logs.append(current_multiline_entry)
                current_multiline_entry = line
            elif detect_multiline and current_multiline_entry:
                # This is a continuation of the previous log entry
                current_multiline_entry += '\n' + line
            else:
                # Single line entry or multiline detection is off
                if current_multiline_entry:
                    processed_logs.append(current_multiline_entry)
                    current_multiline_entry = None
                processed_logs.append(line)
        
        return processed_logs
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            chunk = []
            for line in file:
                chunk.append(line)
                
                if len(chunk) >= chunk_size:
                    logs.extend(process_chunk(chunk))
                    chunk = []
            
            # Process remaining lines
            if chunk:
                logs.extend(process_chunk(chunk))
            
            # Add the last multiline entry if exists
            if current_multiline_entry:
                logs.append(current_multiline_entry)
                
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return []
    
    return logs

def store_logs(logs, output_path):
    """
    Stores the ingested logs in a structured format.
    
    Args:
        logs (list): The list of structured log entries.
        output_path (str): The path to save the structured logs.
    """
    with open(output_path, 'w') as file:
        for log in logs:
            file.write(f"{log}\n")