import json
import os

def ingest_json_logs(file_path):
    """
    Enhanced JSON log ingestion supporting multiple JSON formats:
    - Standard JSON array of log objects
    - JSONL (newline-delimited JSON)
    - Nested JSON structures with error arrays
    - Single JSON objects

    Args:
        file_path (str): The path to the JSON log file.

    Returns:
        list: A list of dictionaries containing the extracted log information.
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"The file {file_path} does not exist.")

    logs = []
    
    with open(file_path, 'r') as file:
        content = file.read().strip()
        
        try:
            # Try to parse as standard JSON first
            data = json.loads(content)
            
            if isinstance(data, list):
                # Standard JSON array format
                logs = data
            elif isinstance(data, dict):
                # Handle nested structures
                if 'errors' in data:
                    logs = data['errors']
                elif 'logs' in data:
                    logs = data['logs']
                elif 'events' in data:
                    logs = data['events']
                else:
                    # Single log object
                    logs = [data]
            else:
                logs = [data]
                
        except json.JSONDecodeError:
            # Try JSONL format (newline-delimited JSON)
            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if line:
                    try:
                        log_entry = json.loads(line)
                        logs.append(log_entry)
                    except json.JSONDecodeError as e:
                        print(f"Warning: Skipping invalid JSON on line {line_num}: {e}")
                        continue
    
    return logs

def ingest_json(file_path):
    """
    Ingest JSON logs from the specified file path.

    Args:
        file_path (str): The path to the JSON log file.

    Returns:
        list: A list of dictionaries containing the extracted log information.
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"The file {file_path} does not exist.")

    with open(file_path, 'r') as file:
        try:
            logs = json.load(file)
        except json.JSONDecodeError as e:
            raise ValueError(f"Error decoding JSON: {e}")

    return logs

def extract_relevant_info(logs):
    """
    Enhanced extraction of relevant information from JSON logs supporting various formats.

    Args:
        logs (list): A list of dictionaries containing log information.

    Returns:
        list: A list of dictionaries with relevant fields extracted and normalized.
    """
    relevant_logs = []
    
    for log in logs:
        if not isinstance(log, dict):
            continue
            
        # Initialize normalized log entry
        relevant_log = {
            'timestamp': None,
            'level': None,
            'message': None,
            'service': None,
            'error_type': None,
            'severity': None,
            'details': None
        }
        
        # Extract timestamp (various possible field names)
        timestamp_fields = ['timestamp', 'time', 'datetime', '@timestamp', 'ts']
        for field in timestamp_fields:
            if field in log:
                relevant_log['timestamp'] = log[field]
                break
        
        # Extract log level (various possible field names)
        level_fields = ['level', 'severity', 'log_level', 'priority']
        for field in level_fields:
            if field in log:
                relevant_log['level'] = log[field].upper() if isinstance(log[field], str) else log[field]
                break
        
        # Extract message (various possible field names)
        message_fields = ['message', 'msg', 'error', 'description', 'text', 'content']
        for field in message_fields:
            if field in log:
                relevant_log['message'] = log[field]
                break
        
        # Extract service/component
        service_fields = ['service', 'component', 'module', 'source', 'logger']
        for field in service_fields:
            if field in log:
                relevant_log['service'] = log[field]
                break
        
        # Extract error type
        type_fields = ['type', 'error_type', 'category', 'class']
        for field in type_fields:
            if field in log:
                relevant_log['error_type'] = log[field]
                break
        
        # Extract severity
        if 'severity' in log:
            relevant_log['severity'] = log['severity']
        
        # Extract details
        details_fields = ['details', 'detail', 'stack_trace', 'trace', 'context']
        for field in details_fields:
            if field in log:
                relevant_log['details'] = log[field]
                break
        
        # If no message found, try to construct one from available fields
        if not relevant_log['message']:
            if relevant_log['error_type']:
                relevant_log['message'] = relevant_log['error_type']
            elif relevant_log['details']:
                relevant_log['message'] = relevant_log['details']
            else:
                # Use the entire log as string
                relevant_log['message'] = str(log)
        
        relevant_logs.append(relevant_log)

    return relevant_logs