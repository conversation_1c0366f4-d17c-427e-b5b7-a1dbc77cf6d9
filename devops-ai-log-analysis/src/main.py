from ingestion.ingest_text import ingest_text_logs
from ingestion.ingest_xml import ingest_xml_logs
from ingestion.ingest_json import ingest_json_logs
from ingestion.ingest_timeseries import ingest_timeseries_logs
from preprocessing.preprocess_text import preprocess_text
from preprocessing.preprocess_xml import preprocess_xml
from preprocessing.preprocess_json import preprocess_json
from preprocessing.preprocess_timeseries import preprocess_timeseries
from error_classification.classify_errors import classify_errors
from root_cause_analysis.analyze_root_cause import analyze_root_cause
from anomaly_detection.detect_anomalies import detect_anomalies
from trend_detection.detect_trends import detect_trends

def main():
    """
    Enhanced main function with performance monitoring and large file support.
    """
    import time
    import os
    
    print("DevOps AI Log Analysis - Enhanced Version")
    print("=" * 60)
    
    start_time = time.time()
    
    # Check file sizes
    log_files = {
        'text': 'data/raw/logs.txt',
        'multiline': 'data/raw/multiline_logs.txt',
        'xml': 'data/raw/logs.xml',
        'json': 'data/raw/logs.json',
        'csv': 'data/raw/logs.csv'
    }
    
    for file_type, file_path in log_files.items():
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"📁 {file_type.upper()} file: {size:,} bytes")
    
    print("\n🔄 Stage 1: Ingesting logs...")
    ingest_start = time.time()
    
    # Ingest logs from various formats with enhanced multiline support
    text_logs = ingest_text_logs('data/raw/logs.txt')
    multiline_logs = ingest_text_logs('data/raw/multiline_logs.txt', detect_multiline=True)
    xml_logs = ingest_xml_logs('data/raw/logs.xml')
    json_logs = ingest_json_logs('data/raw/logs.json')
    timeseries_logs = ingest_timeseries_logs('data/raw/logs.csv')
    
    # Combine text logs for comprehensive analysis
    all_text_logs = text_logs + multiline_logs
    
    ingest_time = time.time() - ingest_start
    print(f"   ✓ Ingested {len(all_text_logs)} text entries, {len(xml_logs)} XML entries, {len(json_logs)} JSON entries")
    print(f"   ⏱️  Ingestion completed in {ingest_time:.2f} seconds")
    
    print("\n🔄 Stage 2: Preprocessing logs...")
    preprocess_start = time.time()
    
    # Preprocess the ingested logs
    preprocessed_text = preprocess_text(all_text_logs)
    preprocessed_xml = preprocess_xml(xml_logs)
    preprocessed_json = preprocess_json(json_logs)
    preprocessed_timeseries = preprocess_timeseries(timeseries_logs)
    
    preprocess_time = time.time() - preprocess_start
    print(f"   ✓ Preprocessed {len(preprocessed_text)} text entries")
    print(f"   ⏱️  Preprocessing completed in {preprocess_time:.2f} seconds")
    
    print("\n🔄 Stage 3: Enhanced error classification...")
    classify_start = time.time()
    
    # Enhanced error classification with multiline support
    classified_errors = classify_errors(preprocessed_text)
    
    classify_time = time.time() - classify_start
    print(f"   ✓ Classified {classified_errors['total_errors']} errors")
    print(f"   ✓ Detected {classified_errors['multiline_count']} multiline error blocks")
    print(f"   ⏱️  Classification completed in {classify_time:.2f} seconds")
    
    print("\n🔄 Stage 4: Enhanced root cause analysis...")
    rca_start = time.time()
    
    # Enhanced root cause analysis
    root_causes = analyze_root_cause(classified_errors)
    
    rca_time = time.time() - rca_start
    print(f"   ✓ Analyzed root causes with {len(root_causes['recommendations'])} recommendations")
    print(f"   ⏱️  Root cause analysis completed in {rca_time:.2f} seconds")
    
    print("\n🔄 Stage 5: Enhanced anomaly detection...")
    anomaly_start = time.time()
    
    # Enhanced anomaly detection
    anomalies = detect_anomalies(preprocessed_json)
    
    anomaly_time = time.time() - anomaly_start
    print(f"   ✓ Detected {anomalies['count']} anomalies")
    print(f"   ✓ High/Critical severity: {anomalies['high_severity_count']} anomalies")
    print(f"   ⏱️  Anomaly detection completed in {anomaly_time:.2f} seconds")
    
    print("\n🔄 Stage 6: Trend analysis...")
    trend_start = time.time()
    
    # Detect trends in the time series data
    trends = detect_trends(preprocessed_timeseries)
    
    trend_time = time.time() - trend_start
    print(f"   ✓ Analyzed trends for {len(trends['trends'])} metrics")
    print(f"   ⏱️  Trend analysis completed in {trend_time:.2f} seconds")
    
    total_time = time.time() - start_time
    print(f"\n⏱️  Total processing time: {total_time:.2f} seconds")
    
    # Enhanced output with detailed results
    print("\n" + "=" * 60)
    print("📊 DETAILED ANALYSIS RESULTS")
    print("=" * 60)
    
    print_enhanced_results(classified_errors, root_causes, anomalies, trends)

def print_enhanced_results(classified_errors, root_causes, anomalies, trends):
    """Print enhanced results with better formatting"""
    
    print("\n🔍 ERROR CLASSIFICATION SUMMARY:")
    print("-" * 40)
    for category, count in classified_errors['summary'].items():
        if count > 0:
            severity = classified_errors['severity_scores'].get(category, 0)
            print(f"  {category.replace('_', ' ').title()}: {count} errors (Severity: {severity:.1f}/10)")
    
    print(f"\n📋 MULTILINE ERROR BLOCKS: {classified_errors['multiline_count']}")
    if classified_errors['multiline_errors']:
        for error in classified_errors['multiline_errors'][:3]:
            print(f"  - {error['type']}: {error['line_count']} lines")
    
    print(f"\n🎯 ROOT CAUSE ANALYSIS:")
    print("-" * 40)
    if root_causes['most_common_cause']:
        cause, count = root_causes['most_common_cause']
        print(f"  Most Common Cause: {cause.replace('_', ' ').title()} ({count} occurrences)")
    
    print(f"\n💡 TOP RECOMMENDATIONS:")
    for rec in root_causes['recommendations'][:3]:
        print(f"  [{rec['priority']}] {rec['cause'].replace('_', ' ').title()}")
        print(f"      Action: {rec['action']}")
        print(f"      Impact: {rec['impact']}")
    
    print(f"\n🚨 ANOMALY DETECTION:")
    print("-" * 40)
    print(f"  Total Anomalies: {anomalies['count']}")
    for severity, count in anomalies['severity_distribution'].items():
        print(f"  {severity}: {count} anomalies")
    
    print(f"\n📈 TREND ANALYSIS:")
    print("-" * 40)
    for metric, data in trends['trends'].items():
        direction = data['direction']
        icon = "📈" if direction == "increasing" else "📉" if direction == "decreasing" else "➡️"
        print(f"  {icon} {metric.replace('_', ' ').title()}: {direction} (slope: {data['slope']:.3f})")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()