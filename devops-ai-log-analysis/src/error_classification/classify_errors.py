from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report
from sklearn.feature_extraction.text import TfidfVectorizer
import pandas as pd
import joblib
import re
import numpy as np
from sklearn.ensemble import GradientBoostingClassifier
from collections import Counter, defaultdict
import torch
import torch.nn as nn
import torch.nn.functional as F

class ErrorClassifier:
    def __init__(self, model=None):
        self.model = model if model else RandomForestClassifier()

    def train(self, X, y):
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        self.model.fit(X_train, y_train)
        y_pred = self.model.predict(X_test)
        print(classification_report(y_test, y_pred))

    def predict(self, X):
        return self.model.predict(X)

    def save_model(self, filepath):
        joblib.dump(self.model, filepath)

    def load_model(self, filepath):
        self.model = joblib.load(filepath)

class TransformerErrorClassifier(nn.Module):
    """Transformer-based error classification model"""
    def __init__(self, input_dim, model_dim, num_classes, num_heads, num_layers, dropout=0.1):
        super(TransformerErrorClassifier, self).__init__()
        self.embedding = nn.Embedding(input_dim, model_dim)
        self.transformer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(model_dim, num_heads, model_dim * 2, dropout), num_layers)
        self.fc = nn.Linear(model_dim, num_classes)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        x = self.embedding(x) * np.sqrt(self.embedding.embedding_dim)
        x = self.dropout(x)
        x = self.transformer(x)
        x = self.fc(x.mean(dim=1))
        return F.log_softmax(x, dim=1)

class GenerativeErrorAnalyzer:
    """Generative AI-powered error analysis and explanation"""
    def __init__(self, model_name="gpt-3.5-turbo"):
        import openai
        openai.api_key = "your-api-key"
        self.model_name = model_name

    def analyze(self, error_message):
        response = openai.ChatCompletion.create(
            model=self.model_name,
            messages=[{"role": "user", "content": f"Analyze and explain this error: {error_message}"}]
        )
        return response['choices'][0]['message']['content']

def classify_errors(logs):
    """
    Classify errors from preprocessed logs using rule-based approach with multiline support.
    
    Args:
        logs (list): List of preprocessed log entries (may include multiline entries or strings)
        
    Returns:
        dict: Classification results with error categories, multiline blocks, and counts
    """
    error_categories = {
        'connection_errors': [],
        'database_errors': [],
        'authentication_errors': [],
        'memory_errors': [],
        'network_errors': [],
        'application_errors': [],
        'stack_trace_errors': [],
        'timeout_errors': [],
        'general_errors': []
    }
    
    # Enhanced patterns for multiline error detection
    multiline_patterns = {
        'java_stack_trace': re.compile(r'(Exception|Error).*\n(\s+at\s+.*\n?)+', re.MULTILINE),
        'python_traceback': re.compile(r'Traceback \(most recent call last\):.*\n(.*\n)*.*Error:.*', re.MULTILINE),
        'sql_error_block': re.compile(r'SQL.*Error.*\n(.*\n)*.*Query:.*', re.MULTILINE | re.IGNORECASE),
        'connection_timeout': re.compile(r'(Connection|Timeout).*\n(.*timeout.*\n?)*', re.MULTILINE | re.IGNORECASE)
    }
    
    # Enhanced single line error patterns
    single_line_patterns = {
        'connection_errors': [
            'connection', 'connect', 'redis', 'timeout', 'unreachable', 
            'connection refused', 'connection failed', 'connection timed out'
        ],
        'database_errors': [
            'sql', 'database', 'table', 'db', 'query', 'constraint',
            'deadlock', 'lock wait timeout', 'connection pool', 'SQLException'
        ],
        'authentication_errors': [
            'auth', 'credentials', 'login', 'unauthorized', 'forbidden',
            'authentication', 'permission denied'
        ],
        'memory_errors': [
            'memory', 'disk space', 'storage', 'heap', 'oom', 'out of memory',
            'OutOfMemoryError', 'MemoryError'
        ],
        'network_errors': [
            'network', 'http', '500', '404', '503', 'dns', 'host',
            'service unavailable', 'high load'
        ],
        'timeout_errors': [
            'timeout', 'timed out', 'deadline exceeded', 'connection timeout',
            'lock wait timeout'
        ],
        'application_errors': [
            'null pointer', 'segmentation fault', 'assertion', 'panic',
            'fatal', 'critical', 'health check failed'
        ]
    }
    
    multiline_errors = []
    
    # Convert logs to string format if they're dictionaries
    processed_logs = []
    log_metadata = []  # Store additional metadata for JSON logs
    
    for log in logs:
        if isinstance(log, dict):
            # For JSON logs, extract the message and preserve metadata
            message = log.get('message', str(log))
            processed_logs.append(message)
            
            # Store metadata for enhanced classification
            metadata = {
                'level': log.get('level', '').upper(),
                'service': log.get('service', ''),
                'error_type': log.get('error_type', ''),
                'severity': log.get('severity', ''),
                'details': log.get('details', ''),
                'original_log': log
            }
            log_metadata.append(metadata)
        else:
            processed_logs.append(str(log))
            log_metadata.append({})  # Empty metadata for non-JSON logs
    
    # Join all logs for multiline pattern detection
    full_log_text = '\n'.join(processed_logs)
    
    # Check for multiline patterns first
    for pattern_name, pattern in multiline_patterns.items():
        matches = pattern.finditer(full_log_text)
        for match in matches:
            multiline_errors.append({
                'type': pattern_name,
                'content': match.group(),
                'line_count': len(match.group().split('\n'))
            })
            error_categories['stack_trace_errors'].append(match.group())
    
    # Process individual log entries
    for i, log in enumerate(processed_logs):
        log_lower = log.lower()
        metadata = log_metadata[i] if i < len(log_metadata) else {}
        
        # Skip if this log is already part of a multiline error
        is_part_of_multiline = any(log in me['content'] for me in multiline_errors)
        
        # Enhanced error detection for JSON logs
        is_error = False
        
        # Check log level from metadata (for JSON logs)
        if metadata.get('level') in ['ERROR', 'CRITICAL', 'FATAL', 'EXCEPTION']:
            is_error = True
        # Check if it's an error/warning line in the message
        elif any(indicator in log.upper() for indicator in ['ERROR', 'FATAL', 'CRITICAL', 'EXCEPTION', 'FAILED']):
            is_error = True
        # Check error type from metadata
        elif metadata.get('error_type'):
            is_error = True
        # Check severity from metadata
        elif metadata.get('severity') and str(metadata.get('severity', '')).upper() in ['HIGH', 'CRITICAL', 'SEVERE']:
            is_error = True
        
        if is_error and not is_part_of_multiline:
            # Enhanced categorization using both message and metadata
            categorized = False
            
            # First try to categorize using error_type from JSON metadata
            if metadata.get('error_type'):
                error_type_lower = metadata['error_type'].lower()
                for category, keywords in single_line_patterns.items():
                    if any(keyword in error_type_lower for keyword in keywords):
                        error_categories[category].append(log)
                        categorized = True
                        break
            
            # If not categorized by error_type, try message content
            if not categorized:
                for category, keywords in single_line_patterns.items():
                    if any(keyword in log_lower for keyword in keywords):
                        error_categories[category].append(log)
                        categorized = True
                        break
            
            # If still not categorized, try service-based categorization
            if not categorized and metadata.get('service'):
                service_lower = metadata['service'].lower()
                if any(keyword in service_lower for keyword in ['db', 'database', 'sql']):
                    error_categories['database_errors'].append(log)
                    categorized = True
                elif any(keyword in service_lower for keyword in ['auth', 'login', 'security']):
                    error_categories['authentication_errors'].append(log)
                    categorized = True
                elif any(keyword in service_lower for keyword in ['network', 'http', 'api']):
                    error_categories['network_errors'].append(log)
                    categorized = True
                elif any(keyword in service_lower for keyword in ['cache', 'redis', 'memcache']):
                    error_categories['connection_errors'].append(log)
                    categorized = True
            
            if not categorized:
                error_categories['general_errors'].append(log)
        
        # Also check WARN level logs for potential issues
        elif metadata.get('level') == 'WARNING' or 'WARN' in log.upper():
            # Classify warnings that might indicate issues
            for category, keywords in single_line_patterns.items():
                if any(keyword in log_lower for keyword in keywords):
                    error_categories[category].append(log)
                    break
    
    # Calculate severity scores
    severity_scores = {}
    for category, errors in error_categories.items():
        if errors:
            severity_scores[category] = calculate_severity_score(category, errors)
    
    # Convert to counts for summary
    error_summary = {category: len(errors) for category, errors in error_categories.items()}
    
    return {
        'categories': error_categories,
        'multiline_errors': multiline_errors,
        'severity_scores': severity_scores,
        'summary': error_summary,
        'total_errors': sum(error_summary.values()),
        'multiline_count': len(multiline_errors),
        'classification_details': {
            'patterns_detected': list(severity_scores.keys()),
            'highest_severity_category': max(severity_scores.items(), key=lambda x: x[1])[0] if severity_scores else 'none'
        }
    }

def calculate_severity_score(category, errors):
    """Calculate severity score based on error category and patterns"""
    severity_weights = {
        'stack_trace_errors': 9,
        'application_errors': 8,
        'database_errors': 7,
        'authentication_errors': 6,
        'network_errors': 5,
        'timeout_errors': 5,
        'connection_errors': 4,
        'memory_errors': 7,
        'general_errors': 3
    }
    
    base_score = severity_weights.get(category, 3)
    error_count = len(errors)
    
    # Increase severity based on frequency
    if error_count > 10:
        multiplier = 1.5
    elif error_count > 5:
        multiplier = 1.2
    else:
        multiplier = 1.0
    
    return min(10, base_score * multiplier)

# Example usage
if __name__ == "__main__":
    logs = []  # Load or preprocess your logs here
    classifier = ErrorClassifier()
    classifier.train(X=[], y=[])  # Replace with actual data
    results = classify_errors(logs)
    print(results)