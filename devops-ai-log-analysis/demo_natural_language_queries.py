#!/usr/bin/env python3
"""
Natural Language Query Interface - Demo and Setup Script
Demonstrates the natural language query capabilities of the DevOps AI Log Analysis system.
"""

import os
import sys
import json
import time
from datetime import datetime

# Add src to path
sys.path.append('src')

from ai_enhanced.llm_integration import NaturalLanguageQueryProcessor
from ai_enhanced.search_engine import NaturalLanguageSearchEngine

def demo_natural_language_queries():
    """Demonstrate natural language query capabilities."""
    print("🤖 Natural Language Query Interface Demo")
    print("=" * 60)
    
    # Initialize components
    processor = NaturalLanguageQueryProcessor()
    search_engine = NaturalLanguageSearchEngine()
    
    # Test queries
    test_queries = [
        "Show me all errors in the last hour",
        "Find authentication failures from user-service",
        "What happened with database connections today?",
        "Show me critical issues in the payment system",
        "Find all timeouts in the API gateway",
        "What performance problems occurred yesterday?",
        "Show me security alerts from the last 24 hours",
        "Find all exceptions in the order processing service"
    ]
    
    print(f"Testing {len(test_queries)} natural language queries...")
    print()
    
    for i, query in enumerate(test_queries, 1):
        print(f"🔍 Query {i}: {query}")
        
        # Process query
        search_params = processor.process_query(query)
        
        # Execute search
        results = search_engine.execute_search(search_params)
        
        # Display results
        print(f"   📊 Type: {results['query_type']}")
        print(f"   🎯 Confidence: {results['confidence']:.2f}")
        print(f"   📈 Results: {results['total_results']} entries found")
        print(f"   ⚡ Processing: {results['processing_method']}")
        print(f"   🕐 Time: {results['execution_time_ms']}ms")
        
        if results['insights']:
            print(f"   💡 Insights: {len(results['insights'])}")
            for insight in results['insights'][:2]:  # Show first 2 insights
                print(f"      • {insight}")
        
        print()
    
    print("✅ Demo completed successfully!")
    print()
    
    # Show features
    print("🎯 Key Features:")
    print("  • Pattern-based query processing (works without OpenAI)")
    print("  • Time range extraction ('last hour', 'yesterday', etc.)")
    print("  • Service and severity filtering")
    print("  • Error type and keyword detection")
    print("  • Intelligent insights generation")
    print("  • Query type classification")
    print("  • Confidence scoring")
    print("  • Search result caching")
    print()
    
    # Show web interface info
    print("🌐 Web Interface:")
    print("  • Visit: http://localhost:5000/chat")
    print("  • Modern chat interface with real-time suggestions")
    print("  • Example queries and search history")
    print("  • Rich result visualization")
    print("  • Mobile-responsive design")
    print()

def setup_environment():
    """Set up the environment for natural language queries."""
    print("🔧 Setting up Natural Language Query Environment")
    print("=" * 60)
    
    # Check if OpenAI package is installed
    try:
        import openai
        print("✅ OpenAI package is installed")
    except ImportError:
        print("❌ OpenAI package not found")
        print("   Install with: pip install openai")
    
    # Check for API key
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        print("✅ OpenAI API key is configured")
        print("   🤖 AI-powered query processing is available")
    else:
        print("⚠️  OpenAI API key not configured")
        print("   📋 Pattern-based processing is available")
        print("   🔗 To enable AI features, set OPENAI_API_KEY environment variable")
    
    # Check directory structure
    required_dirs = [
        'src/ai_enhanced',
        'ui/templates',
        'data/raw'
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path} exists")
        else:
            print(f"❌ {dir_path} missing")
    
    print()

def create_sample_queries():
    """Create sample queries file for testing."""
    sample_queries = {
        "error_analysis": [
            "Show me all errors in the last hour",
            "Find critical failures in the payment system",
            "What exceptions occurred in the API gateway?",
            "Show me database connection errors from yesterday"
        ],
        "security_audit": [
            "Find authentication failures from user-service",
            "Show me failed login attempts in the last 30 minutes",
            "What security alerts happened this week?",
            "Find all unauthorized access attempts"
        ],
        "performance_monitoring": [
            "Show me all timeouts in the last 24 hours",
            "Find slow database queries from today",
            "What performance issues occurred yesterday?",
            "Show me high latency requests"
        ],
        "service_monitoring": [
            "What happened in the order processing service?",
            "Show me all logs from the payment gateway",
            "Find issues in the notification service",
            "What's happening with the user management system?"
        ],
        "general_search": [
            "Show me what happened in the last hour",
            "Find all important events from today",
            "What's been happening with the system?",
            "Show me recent activity"
        ]
    }
    
    with open('sample_queries.json', 'w') as f:
        json.dump(sample_queries, f, indent=2)
    
    print("📝 Sample queries saved to sample_queries.json")
    return sample_queries

def interactive_demo():
    """Run interactive demo where user can enter queries."""
    print("🎮 Interactive Natural Language Query Demo")
    print("=" * 60)
    print("Enter natural language queries about your logs.")
    print("Type 'exit' to quit, 'examples' for sample queries.")
    print()
    
    processor = NaturalLanguageQueryProcessor()
    search_engine = NaturalLanguageSearchEngine()
    
    while True:
        try:
            query = input("🔍 Enter your query: ").strip()
            
            if query.lower() == 'exit':
                print("👋 Goodbye!")
                break
            
            if query.lower() == 'examples':
                suggestions = processor.get_query_suggestions()
                print("\n💭 Example queries:")
                for i, suggestion in enumerate(suggestions, 1):
                    print(f"   {i}. {suggestion}")
                print()
                continue
            
            if not query:
                continue
            
            print(f"\n🔄 Processing: {query}")
            
            # Process and execute query
            search_params = processor.process_query(query)
            results = search_engine.execute_search(search_params)
            
            # Display results
            print(f"📊 Type: {results['query_type']}")
            print(f"🎯 Confidence: {results['confidence']:.2f}")
            print(f"📈 Results: {results['total_results']} entries found")
            print(f"⚡ Processing: {results['processing_method']}")
            
            if results['insights']:
                print("💡 Insights:")
                for insight in results['insights']:
                    print(f"   • {insight}")
            
            print()
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            print()

def main():
    """Main function to run demos and setup."""
    print("🚀 DevOps AI Log Analysis - Natural Language Query Interface")
    print("=" * 80)
    print()
    
    # Setup environment
    setup_environment()
    
    # Create sample queries
    create_sample_queries()
    
    # Show menu
    while True:
        print("📋 Choose an option:")
        print("1. Run automated demo")
        print("2. Run interactive demo")
        print("3. Show query suggestions")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == '1':
            print()
            demo_natural_language_queries()
        elif choice == '2':
            print()
            interactive_demo()
        elif choice == '3':
            print()
            processor = NaturalLanguageQueryProcessor()
            suggestions = processor.get_query_suggestions()
            print("💭 Query Suggestions:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"   {i}. {suggestion}")
            print()
        elif choice == '4':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please try again.")
        
        print()

if __name__ == "__main__":
    main()
