2024-01-01 10:00:01 INFO Application started successfully
2024-01-01 10:00:02 INFO Database connection established
2024-01-01 10:00:03 ERROR Failed to connect to Redis server: Connection timeout
2024-01-01 10:00:04 WARN Memory usage is at 85%
2024-01-01 10:00:05 INFO Processing user request for user_id=12345
2024-01-01 10:00:06 ERROR SQL query failed: Table 'users' doesn't exist
2024-01-01 10:00:07 INFO Request processed successfully
2024-01-01 10:00:08 ERROR HTTP 500 Internal Server Error
2024-01-01 10:00:09 INFO Scheduled backup started
2024-01-01 10:00:10 ERROR Backup failed: Insufficient disk space
2024-01-01 10:00:11 WARN CPU usage spike detected: 95%
2024-01-01 10:00:12 INFO User authentication successful
2024-01-01 10:00:13 ERROR Authentication failed: Invalid credentials
2024-01-01 10:00:14 INFO API endpoint /health responded with 200
2024-01-01 10:00:15 ERROR Network timeout occurred