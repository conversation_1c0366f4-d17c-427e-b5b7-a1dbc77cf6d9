<?xml version="1.0" encoding="UTF-8"?>
<logs>
  <log>
    <timestamp>2024-01-01T10:00:01</timestamp>
    <level>INFO</level>
    <message>Application started successfully</message>
    <service>web-app</service>
  </log>
  <log>
    <timestamp>2024-01-01T10:00:03</timestamp>
    <level>ERROR</level>
    <message>Failed to connect to Redis server: Connection timeout</message>
    <service>cache</service>
  </log>
  <log>
    <timestamp>2024-01-01T10:00:06</timestamp>
    <level>ERROR</level>
    <message>SQL query failed: Table 'users' doesn't exist</message>
    <service>database</service>
  </log>
</logs>