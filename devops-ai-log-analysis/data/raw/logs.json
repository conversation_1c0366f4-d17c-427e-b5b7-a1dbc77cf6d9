[{"timestamp": "2024-01-01T10:00:01", "level": "INFO", "message": "Application started successfully", "service": "web-app"}, {"timestamp": "2024-01-01T10:00:03", "level": "ERROR", "message": "Failed to connect to Redis server: Connection timeout", "service": "cache"}, {"timestamp": "2024-01-01T10:00:06", "level": "ERROR", "message": "SQL query failed: Table 'users' doesn't exist", "service": "database"}, {"timestamp": "2024-01-01T10:00:08", "level": "ERROR", "message": "HTTP 500 Internal Server Error", "service": "web-app"}, {"timestamp": "2024-01-01T10:00:13", "level": "ERROR", "message": "Authentication failed: In<PERSON>id credentials", "service": "auth"}]