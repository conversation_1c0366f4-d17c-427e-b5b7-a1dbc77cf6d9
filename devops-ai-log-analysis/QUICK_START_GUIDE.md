# Quick Start Guide: Enhanced Anomaly Detection System

## 🚀 Getting Started with Advanced AI-Powered Anomaly Detection

### Basic Usage

#### 1. Import the Enhanced System
```python
from anomaly_detection.advanced_anomaly_detection import (
    detect_anomalies_advanced,
    detect_anomalies_with_monitoring,
    configure_advanced_detection
)
```

#### 2. Simple Anomaly Detection
```python
# Your log data (list of dictionaries)
log_data = [
    {
        'timestamp': '2024-01-15T10:00:00Z',
        'level': 'ERROR',
        'message': 'Connection timeout to database',
        'service': 'web-api'
    },
    # ... more log entries
]

# Run advanced detection
results = detect_anomalies_advanced(log_data)

print(f"Found {results['count']} anomalies")
print(f"AI Recommendations: {len(results['ai_recommendations'])}")
```

#### 3. Enhanced Detection with All Features
```python
# Full-featured detection
results = detect_anomalies_advanced(
    log_data,
    use_ml=True,              # Enable ML algorithms
    use_generative_ai=True,   # Enable AI insights
    real_time_mode=False      # Set to True for streaming
)

# Access results
anomalies = results['anomalies']
ai_insights = results['ai_insights']
recommendations = results['ai_recommendations']
performance = results['performance_metrics']
```

### Advanced Features

#### 1. Real-Time Processing
```python
from anomaly_detection.advanced_anomaly_detection import RealTimeAnomalyBuffer

# Initialize real-time buffer
buffer = RealTimeAnomalyBuffer(max_size=10000)

# Add logs as they arrive
buffer.add_entry({
    'timestamp': datetime.now().isoformat(),
    'level': 'ERROR',
    'message': 'Critical system error',
    'service': 'payment-service'
})

# Analyze recent data
recent_logs = buffer.get_recent_entries(minutes=5)
results = detect_anomalies_advanced(recent_logs, real_time_mode=True)
```

#### 2. Performance Monitoring
```python
# Run with performance monitoring
results = detect_anomalies_with_monitoring(log_data)

# Check performance metrics
print(f"Detection time: {results['performance']['detection_time']:.3f}s")
print(f"Throughput: {results['performance']['throughput']:.0f} entries/sec")
```

#### 3. Configuration Management
```python
# Configure detection parameters
config = configure_advanced_detection({
    'use_ml': True,
    'use_generative_ai': True,
    'contamination': 0.05,  # 5% expected anomaly rate
    'confidence_threshold': 0.8
})

# Use configuration
results = detect_anomalies_advanced(log_data, **config)
```

### AI-Powered Insights

#### Accessing AI Recommendations
```python
recommendations = results['ai_recommendations']

for rec in recommendations:
    print(f"Category: {rec['category']}")
    print(f"Priority: {rec['priority']}")
    print(f"Confidence: {rec['confidence']:.2f}")
    print("Actions:")
    for action in rec['recommendations']:
        print(f"  • {action}")
```

#### Understanding AI Insights
```python
insights = results['ai_insights']

# Error analysis
error_analysis = insights['error_analysis']
print(f"Primary error type: {error_analysis['primary_error_category']}")
print(f"Most affected service: {error_analysis['most_affected_service']}")

# Root cause hypotheses
hypotheses = insights['root_cause_hypothesis']
for hypothesis in hypotheses:
    print(f"Cause: {hypothesis['hypothesis']}")
    print(f"Confidence: {hypothesis['confidence']:.2f}")
    print(f"Investigation steps: {hypothesis['investigation_steps']}")
```

### Integration Examples

#### 1. Jenkins Pipeline Integration
```python
def analyze_build_logs(log_file_path):
    """Analyze Jenkins build logs for anomalies"""
    
    # Read logs
    with open(log_file_path, 'r') as f:
        raw_logs = f.readlines()
    
    # Convert to structured format
    structured_logs = []
    for line in raw_logs:
        if 'ERROR' in line or 'FAIL' in line:
            structured_logs.append({
                'timestamp': datetime.now().isoformat(),
                'level': 'ERROR',
                'message': line.strip(),
                'service': 'jenkins-build'
            })
    
    # Analyze
    results = detect_anomalies_advanced(structured_logs)
    
    # Generate report
    if results['count'] > 0:
        print(f"⚠️  Build analysis found {results['count']} anomalies")
        return False  # Fail the build
    
    return True  # Build passed
```

#### 2. Real-Time Alert System
```python
import threading
import time

class LogMonitor:
    def __init__(self):
        self.buffer = RealTimeAnomalyBuffer()
        self.running = False
    
    def add_log(self, log_entry):
        """Add new log entry"""
        self.buffer.add_entry(log_entry)
    
    def start_monitoring(self):
        """Start real-time monitoring thread"""
        self.running = True
        monitor_thread = threading.Thread(target=self._monitor_loop)
        monitor_thread.start()
    
    def _monitor_loop(self):
        """Monitoring loop"""
        while self.running:
            recent_logs = self.buffer.get_recent_entries(minutes=1)
            
            if len(recent_logs) > 0:
                results = detect_anomalies_advanced(
                    recent_logs, 
                    real_time_mode=True
                )
                
                # Send alerts for critical anomalies
                critical_anomalies = [
                    a for a in results['anomalies'] 
                    if a.get('severity') == 'CRITICAL'
                ]
                
                if critical_anomalies:
                    self._send_alert(critical_anomalies)
            
            time.sleep(30)  # Check every 30 seconds
    
    def _send_alert(self, anomalies):
        """Send alert for critical anomalies"""
        print(f"🚨 CRITICAL ALERT: {len(anomalies)} critical anomalies detected!")
        for anomaly in anomalies:
            print(f"  - {anomaly.get('type')}: {anomaly.get('explanation')}")

# Usage
monitor = LogMonitor()
monitor.start_monitoring()

# Add logs as they come in
monitor.add_log({
    'timestamp': datetime.now().isoformat(),
    'level': 'CRITICAL',
    'message': 'OutOfMemoryError in payment service',
    'service': 'payment-service'
})
```

### Output Examples

#### Basic Detection Results
```json
{
  "count": 25,
  "severity_distribution": {
    "CRITICAL": 5,
    "HIGH": 12,
    "MEDIUM": 8
  },
  "detection_methods": {
    "ensemble_ml": 15,
    "enhanced_pattern_matching": 10
  },
  "statistics": {
    "avg_confidence": 87.5,
    "max_confidence": 95.0,
    "critical_count": 5
  }
}
```

#### AI Recommendations
```json
{
  "ai_recommendations": [
    {
      "category": "performance",
      "priority": "HIGH",
      "confidence": 0.92,
      "recommendations": [
        "Optimize database queries and indexing",
        "Implement caching strategies",
        "Scale up compute resources"
      ],
      "reasoning": "Detected 12.5 score for performance issues"
    }
  ]
}
```

### Best Practices

#### 1. Configuration for Different Environments
```python
# Development environment
dev_config = configure_advanced_detection({
    'contamination': 0.1,      # Higher tolerance
    'use_generative_ai': False  # Faster processing
})

# Production environment
prod_config = configure_advanced_detection({
    'contamination': 0.03,     # Lower tolerance
    'use_generative_ai': True, # Full AI insights
    'real_time_mode': True     # Real-time processing
})
```

#### 2. Error Handling
```python
try:
    results = detect_anomalies_advanced(log_data)
except Exception as e:
    print(f"Detection failed: {e}")
    # Fallback to basic detection
    results = {'anomalies': [], 'count': 0}
```

#### 3. Performance Optimization
```python
# For large datasets, disable AI insights for speed
if len(log_data) > 10000:
    results = detect_anomalies_advanced(
        log_data,
        use_ml=True,
        use_generative_ai=False  # Disable for speed
    )
else:
    results = detect_anomalies_advanced(log_data)  # Full features
```

### Support and Troubleshooting

#### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
   ```bash
   pip install -r requirements.txt
   ```

2. **Performance Issues**: For large datasets, disable AI insights
   ```python
   results = detect_anomalies_advanced(data, use_generative_ai=False)
   ```

3. **Memory Issues**: Use real-time mode for streaming data
   ```python
   results = detect_anomalies_advanced(data, real_time_mode=True)
   ```

#### Getting Help

- Run the test suite: `python test_advanced_anomaly_detection.py`
- Run the demo: `python demo_simple_advanced.py`
- Check the comprehensive summary: `ENHANCED_SYSTEM_SUMMARY.md`

Your enhanced DevOps AI log analysis system is now ready for production use! 🚀
