#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from root_cause_analysis.unified_root_cause_analyzer import UnifiedRootCauseAnalyzer

# Test rule-based recommendations (simulate LLM failure)
analyzer = UnifiedRootCauseAnalyzer()

# Sample log text with errors
test_logs = """
2024-01-01 10:00:00 ERROR [Database] Connection timeout to database server postgres://localhost:5432
2024-01-01 10:01:00 ERROR [Application] Failed to authenticate user with token invalid_token_123
"""

print("=== TESTING RULE-BASED RECOMMENDATIONS (simulating LLM failure) ===")

# Temporarily disable LLM to test rule-based fallback
import unittest.mock
with unittest.mock.patch('ai_enhanced.llm_integration.NaturalLanguageQueryProcessor') as mock_processor:
    # Make LLM processor fail
    mock_processor.side_effect = Exception("Simulated LLM failure")
    
    results = analyzer.analyze_log_text(test_logs)

print(f"\n=== RULE-BASED RECOMMENDATIONS ({len(results.get('recommendations', []))}) ===")
for i, rec in enumerate(results.get('recommendations', [])):
    print(f"\nRule-based Recommendation {i+1}:")
    print(f"  Problem: {rec.get('problem', 'N/A')}")
    print(f"  Error Message: {rec.get('error_message', 'NOT SET')}")
    print(f"  Error Line Number: {rec.get('error_line_number', 'NOT SET')}")
    print(f"  Error Category: {rec.get('error_category', 'NOT SET')}")
    print(f"  Source: {rec.get('source', 'NOT SET')}")
