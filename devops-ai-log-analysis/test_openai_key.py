import openai
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Retrieve the OpenAI API key
api_key = os.getenv('OPENAI_API_KEY')

if not api_key:
    print("Error: OPENAI_API_KEY is not set in the .env file.")
    exit(1)

# Set the API key
openai.api_key = api_key

# Test the API key
try:
    response = openai.Model.list()
    print("OpenAI API key is valid. Models available:", response.data)
except Exception as e:
    print("Error: OpenAI API key validation failed:", e)
